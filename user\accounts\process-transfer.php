<?php
require_once '../../config/config.php';
requireLogin();

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get and validate input
    $from_account = trim($_POST['from_account'] ?? '');
    $to_account = trim($_POST['to_account'] ?? '');
    $amount = floatval($_POST['amount'] ?? 0);
    $note = trim($_POST['note'] ?? '');

    // Validation
    if (empty($from_account) || empty($to_account)) {
        throw new Exception('Please select both from and to accounts');
    }

    if ($from_account === $to_account) {
        throw new Exception('Cannot transfer to the same account');
    }

    if ($amount <= 0) {
        throw new Exception('Transfer amount must be greater than zero');
    }

    // Get user information
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();

    if (!$user) {
        throw new Exception('User not found');
    }

    // Start transaction
    $db->beginTransaction();

    try {
        // Handle different account types
        $from_balance = 0;
        $to_balance = 0;

        // Get current balances
        if ($from_account === 'main_account') {
            $from_balance = $user['balance'];
        } else {
            // Virtual card
            $card_id = str_replace('virtual_card_', '', $from_account);
            $card_query = "SELECT card_balance FROM virtual_cards WHERE card_id = ? AND account_id = ?";
            $card_result = $db->query($card_query, [$card_id, $user_id]);
            $card = $card_result->fetch_assoc();
            if (!$card) {
                throw new Exception('Source card not found');
            }
            $from_balance = $card['card_balance'];
        }

        // Check sufficient funds
        if ($from_balance < $amount) {
            throw new Exception('Insufficient funds in source account');
        }

        // Process the transfer
        if ($from_account === 'main_account') {
            // From main account
            $new_balance = $from_balance - $amount;
            $update_query = "UPDATE accounts SET balance = ? WHERE id = ?";
            $db->query($update_query, [$new_balance, $user_id]);
        } else {
            // From virtual card
            $card_id = str_replace('virtual_card_', '', $from_account);
            $new_balance = $from_balance - $amount;
            $update_query = "UPDATE virtual_cards SET card_balance = ? WHERE card_id = ? AND account_id = ?";
            $db->query($update_query, [$new_balance, $card_id, $user_id]);
        }

        if ($to_account === 'main_account') {
            // To main account
            $new_balance = $user['balance'] + $amount;
            $update_query = "UPDATE accounts SET balance = ? WHERE id = ?";
            $db->query($update_query, [$new_balance, $user_id]);
        } else {
            // To virtual card
            $card_id = str_replace('virtual_card_', '', $to_account);
            $card_query = "SELECT card_balance FROM virtual_cards WHERE card_id = ? AND account_id = ?";
            $card_result = $db->query($card_query, [$card_id, $user_id]);
            $card = $card_result->fetch_assoc();
            if (!$card) {
                throw new Exception('Destination card not found');
            }
            $new_balance = $card['card_balance'] + $amount;
            $update_query = "UPDATE virtual_cards SET card_balance = ? WHERE card_id = ? AND account_id = ?";
            $db->query($update_query, [$new_balance, $card_id, $user_id]);
        }

        // Create transaction records
        $reference_number = 'INT' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // Debit transaction (from account)
        $debit_query = "INSERT INTO transactions (
            user_id, transaction_type, amount, currency, description,
            reference_number, status, created_at
        ) VALUES (?, 'debit', ?, ?, ?, ?, 'completed', NOW())";

        $from_description = "Internal transfer to " . ($to_account === 'main_account' ? 'Main Account' : 'Virtual Card');
        if (!empty($note)) {
            $from_description .= " - " . $note;
        }

        $db->query($debit_query, [
            $user_id, $amount, $user['currency'], $from_description, $reference_number
        ]);

        // Credit transaction (to account)
        $credit_query = "INSERT INTO transactions (
            user_id, transaction_type, amount, currency, description,
            reference_number, status, created_at
        ) VALUES (?, 'credit', ?, ?, ?, ?, 'completed', NOW())";

        $to_description = "Internal transfer from " . ($from_account === 'main_account' ? 'Main Account' : 'Virtual Card');
        if (!empty($note)) {
            $to_description .= " - " . $note;
        }

        $db->query($credit_query, [
            $user_id, $amount, $user['currency'], $to_description, $reference_number
        ]);

        // If virtual cards are involved, create virtual card transactions
        if ($from_account !== 'main_account') {
            $card_id = str_replace('virtual_card_', '', $from_account);
            $card_tx_query = "INSERT INTO virtual_card_transactions (
                card_id, account_id, transaction_type, amount, currency, description, 
                reference_number, status, created_at
            ) VALUES (?, ?, 'debit', ?, ?, ?, ?, 'completed', NOW())";
            
            $db->query($card_tx_query, [
                $card_id, $user_id, $amount, $user['currency'], $from_description, $reference_number
            ]);
        }

        if ($to_account !== 'main_account') {
            $card_id = str_replace('virtual_card_', '', $to_account);
            $card_tx_query = "INSERT INTO virtual_card_transactions (
                card_id, account_id, transaction_type, amount, currency, description, 
                reference_number, status, created_at
            ) VALUES (?, ?, 'credit', ?, ?, ?, ?, 'completed', NOW())";
            
            $db->query($card_tx_query, [
                $card_id, $user_id, $amount, $user['currency'], $to_description, $reference_number
            ]);
        }

        // Commit transaction
        $db->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Transfer completed successfully',
            'reference_number' => $reference_number,
            'amount' => $amount,
            'currency' => $user['currency']
        ]);

    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
