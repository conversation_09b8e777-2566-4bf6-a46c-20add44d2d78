/**
 * Card Request Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Card request page loaded');
    initializeCardRequest();
});

/**
 * Initialize card request functionality
 */
function initializeCardRequest() {
    // Add event listeners
    const limitInput = document.getElementById('requested_limit');
    if (limitInput) {
        limitInput.addEventListener('input', validateLimit);
    }

    const form = document.querySelector('.card-request-form');
    if (form) {
        form.addEventListener('submit', handleFormSubmission);
    }

    // Initialize limit display
    updateLimitDisplay();
}

/**
 * Set limit amount
 */
function setLimit(amount) {
    const limitInput = document.getElementById('requested_limit');
    if (limitInput) {
        limitInput.value = amount;
        validateLimit();
        updateLimitDisplay();
        
        // Visual feedback
        const buttons = document.querySelectorAll('.limit-btn');
        buttons.forEach(btn => btn.classList.remove('active'));
        
        const clickedBtn = event.target;
        clickedBtn.classList.add('active');
        
        setTimeout(() => {
            clickedBtn.classList.remove('active');
        }, 200);
    }
}

/**
 * Validate limit input
 */
function validateLimit() {
    const limitInput = document.getElementById('requested_limit');
    const value = parseFloat(limitInput.value);
    
    if (value < 100) {
        limitInput.setCustomValidity('Minimum limit is $100');
    } else if (value > 10000) {
        limitInput.setCustomValidity('Maximum limit is $10,000');
    } else {
        limitInput.setCustomValidity('');
    }
    
    updateLimitDisplay();
}

/**
 * Update limit display
 */
function updateLimitDisplay() {
    const limitInput = document.getElementById('requested_limit');
    const value = parseFloat(limitInput.value) || 0;
    
    // Update any display elements if needed
    const limitInfo = document.querySelector('.limit-info small');
    if (limitInfo && value > 0) {
        const monthlyEstimate = value * 30;
        limitInfo.innerHTML = `
            Daily limit: $${value.toLocaleString()} 
            <span class="text-muted">• Monthly estimate: $${monthlyEstimate.toLocaleString()}</span>
        `;
    }
}

/**
 * Handle form submission
 */
function handleFormSubmission(event) {
    const form = event.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // Validate form
    if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
        form.classList.add('was-validated');
        return;
    }
    
    // Show loading state
    if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
        submitBtn.disabled = true;
        
        // Re-enable button after a delay (in case of errors)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 10000);
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 500px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

/**
 * Format currency input
 */
function formatCurrency(input) {
    let value = input.value.replace(/[^\d.]/g, '');
    
    // Ensure only one decimal point
    const parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
    }
    
    input.value = value;
}

/**
 * Add CSS for active button state
 */
const style = document.createElement('style');
style.textContent = `
    .limit-btn.active {
        background: var(--primary-color) !important;
        color: white !important;
        border-color: var(--primary-color) !important;
        transform: scale(0.95);
    }
    
    .notification-toast {
        animation: slideIn 0.3s ease;
    }
    
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .was-validated .form-control:invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }
    
    .was-validated .form-control:valid {
        border-color: #28a745;
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    }
`;
document.head.appendChild(style);
