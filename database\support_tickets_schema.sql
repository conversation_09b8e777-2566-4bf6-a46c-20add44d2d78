-- Support Tickets Table Schema
-- This table stores user support tickets and help requests

CREATE TABLE IF NOT EXISTS support_tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ticket_number VARCHAR(20) UNIQUE NOT NULL,
    category ENUM('transaction_issue', 'account_access', 'card_issue', 'wire_transfer', 'billing_question', 'technical_issue', 'security_concern', 'other') NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    transaction_id INT NULL,
    contact_method ENUM('email', 'phone', 'both') NOT NULL DEFAULT 'email',
    status ENUM('open', 'in_progress', 'waiting_response', 'resolved', 'closed') NOT NULL DEFAULT 'open',
    assigned_to INT NULL,
    admin_notes TEXT NULL,
    resolution TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_to) REFERENCES admin_users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    INDEX idx_ticket_number (ticket_number)
);

-- Support Ticket Responses Table
-- This table stores responses/messages within a support ticket
CREATE TABLE IF NOT EXISTS support_ticket_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT NOT NULL,
    responder_type ENUM('user', 'admin') NOT NULL,
    responder_id INT NOT NULL,
    message TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    
    INDEX idx_ticket_id (ticket_id),
    INDEX idx_created_at (created_at)
);

-- Support Categories Table
-- This table stores predefined support categories and their descriptions
CREATE TABLE IF NOT EXISTS support_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_key VARCHAR(50) UNIQUE NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default support categories
INSERT INTO support_categories (category_key, category_name, description, sort_order) VALUES
('transaction_issue', 'Transaction Issue', 'Problems with transactions, payments, or transfers', 1),
('account_access', 'Account Access', 'Login issues, password problems, or account lockouts', 2),
('card_issue', 'Card Issue', 'Problems with debit/credit cards or virtual cards', 3),
('wire_transfer', 'Wire Transfer', 'Issues with international wire transfers', 4),
('billing_question', 'Billing Question', 'Questions about fees, charges, or billing', 5),
('technical_issue', 'Technical Issue', 'Website problems, app issues, or technical difficulties', 6),
('security_concern', 'Security Concern', 'Security-related questions or concerns', 7),
('other', 'Other', 'General inquiries or issues not covered by other categories', 8);

-- Support FAQ Table
-- This table stores frequently asked questions
CREATE TABLE IF NOT EXISTS support_faq (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NULL,
    question VARCHAR(500) NOT NULL,
    answer TEXT NOT NULL,
    is_featured BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES support_categories(id) ON DELETE SET NULL,
    
    INDEX idx_category_id (category_id),
    INDEX idx_is_featured (is_featured),
    INDEX idx_is_active (is_active)
);

-- Insert sample FAQ entries
INSERT INTO support_faq (category_id, question, answer, is_featured, sort_order) VALUES
(1, 'How do I report a transaction error?', 'You can report transaction errors by submitting a support ticket through the Help & Support page. Select "Transaction Issue" as the category and provide details about the specific transaction.', TRUE, 1),
(2, 'I forgot my password. How can I reset it?', 'Click on the "Forgot Password" link on the login page. Enter your email address and follow the instructions sent to your email to reset your password.', TRUE, 2),
(3, 'How long do wire transfers take to process?', 'International wire transfers typically take 1-3 business days to process. Domestic transfers are usually completed within 1 business day.', TRUE, 3),
(4, 'What are the fees for wire transfers?', 'Wire transfer fees vary depending on the destination and amount. You can view the current fee schedule in your account dashboard or contact support for specific information.', FALSE, 4),
(5, 'How do I activate my new card?', 'You can activate your new card through the My Cards section in your dashboard or by calling our customer service line at +1 (555) 123-4567.', FALSE, 5);

-- Support Ticket Attachments Table (for future use)
CREATE TABLE IF NOT EXISTS support_ticket_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT NOT NULL,
    response_id INT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    uploaded_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (response_id) REFERENCES support_ticket_responses(id) ON DELETE CASCADE,
    
    INDEX idx_ticket_id (ticket_id),
    INDEX idx_response_id (response_id)
);
