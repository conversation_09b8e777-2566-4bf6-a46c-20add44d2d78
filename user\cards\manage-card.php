<?php
/**
 * Manage Virtual Card Page
 * Manage individual virtual card settings and controls
 */

// Set page variables
$page_title = 'Manage Virtual Card';
$current_page = 'cards';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get card ID from URL
$card_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$card_id) {
    header('Location: index.php');
    exit();
}

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../auth/login.php');
    exit();
}

// Get card information with transaction stats
$card_query = "SELECT vc.*,
                      COUNT(vct.id) as transaction_count,
                      SUM(CASE WHEN vct.transaction_type = 'debit' THEN vct.amount ELSE 0 END) as total_spent,
                      MAX(vct.created_at) as last_transaction_date
               FROM virtual_cards vc
               LEFT JOIN virtual_card_transactions vct ON vc.card_id = vct.card_id
               WHERE vc.card_id = ? AND vc.account_id = ?
               GROUP BY vc.card_id";
$card_result = $db->query($card_query, [$card_id, $user_id]);
$card = $card_result->fetch_assoc();

if (!$card) {
    header('Location: index.php');
    exit();
}

// Calculate additional stats
$available_limit = $card['daily_limit'] - ($card['total_spent'] ?? 0);
$usage_percentage = $card['daily_limit'] > 0 ? (($card['total_spent'] ?? 0) / $card['daily_limit']) * 100 : 0;

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'freeze_card':
                $update_query = "UPDATE virtual_cards SET status = 'blocked' WHERE card_id = ? AND account_id = ?";
                $db->query($update_query, [$card_id, $user_id]);
                $success = "Card has been frozen successfully.";
                $card['status'] = 'blocked';
                break;
                
            case 'unfreeze_card':
                $update_query = "UPDATE virtual_cards SET status = 'active' WHERE card_id = ? AND account_id = ?";
                $db->query($update_query, [$card_id, $user_id]);
                $success = "Card has been unfrozen successfully.";
                $card['status'] = 'active';
                break;
                
            case 'update_limits':
                $daily_limit = floatval($_POST['daily_limit']);
                $monthly_limit = floatval($_POST['monthly_limit']);
                
                if ($daily_limit < 100 || $daily_limit > 10000) {
                    throw new Exception("Daily limit must be between $100 and $10,000.");
                }
                
                if ($monthly_limit < $daily_limit || $monthly_limit > 50000) {
                    throw new Exception("Monthly limit must be between daily limit and $50,000.");
                }
                
                $update_query = "UPDATE virtual_cards SET daily_limit = ?, monthly_limit = ? WHERE card_id = ? AND account_id = ?";
                $db->query($update_query, [$daily_limit, $monthly_limit, $card_id, $user_id]);
                $success = "Card limits updated successfully.";
                $card['daily_limit'] = $daily_limit;
                $card['monthly_limit'] = $monthly_limit;
                break;
                
            default:
                throw new Exception("Invalid action.");
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get card transactions
$transactions_query = "SELECT * FROM virtual_card_transactions 
                      WHERE card_id = ? 
                      ORDER BY created_at DESC 
                      LIMIT 10";
$transactions_result = $db->query($transactions_query, [$card_id]);
$transactions = [];
while ($transaction = $transactions_result->fetch_assoc()) {
    $transactions[] = $transaction;
}

// Set page title
$page_title = 'Manage Virtual Card';

// Include header
require_once __DIR__ . '/../shared/header.php';
?>

<!-- Include Dashboard and Manage Card CSS -->
<link rel="stylesheet" href="../dashboard/dashboard.css">
<link rel="stylesheet" href="manage-card.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once __DIR__ . '/../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once __DIR__ . '/../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1; width: 100%;">

        <!-- Manage Card Hero Section -->
        <div class="manage-card-hero mb-4" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); border-radius: 12px; padding: 2rem; color: white;">
            <div class="hero-content" style="display: flex; justify-content: space-between; align-items: center; gap: 2rem;">
                <div class="hero-main" style="flex: 1;">
                    <div class="hero-title" style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem;">Manage Virtual Card</div>
                    <div class="hero-subtitle" style="font-size: 1.125rem; opacity: 0.9; margin-bottom: 1rem;">Control your virtual card settings and security</div>
                    <div class="hero-stats" style="font-size: 0.875rem; opacity: 0.8;">
                        Card: ****<?php echo substr($card['card_number'], -4); ?> • Status: <?php echo ucfirst($card['status']); ?> • Balance: <?php echo formatCurrency($card['card_balance'], $card['currency']); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="index.php" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left me-2"></i>Back to Cards
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Card Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Card Balance</div>
                    <div class="balance-amount" style="color: #10b981;">
                        <?php echo formatCurrency($card['card_balance'], $card['currency']); ?>
                    </div>
                    <div class="balance-subtitle">Available Funds</div>
                </div>
            </div>

            <!-- Daily Limit Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Daily Limit</div>
                    <div class="balance-amount" style="color: #3b82f6;">
                        <?php echo formatCurrency($card['daily_limit'], $card['currency']); ?>
                    </div>
                    <div class="balance-subtitle">Spending Limit</div>
                </div>
            </div>

            <!-- Total Spent Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total Spent</div>
                    <div class="balance-amount" style="color: #f59e0b;">
                        <?php echo formatCurrency($card['total_spent'] ?? 0, $card['currency']); ?>
                    </div>
                    <div class="balance-subtitle">This Period</div>
                </div>
            </div>

            <!-- Card Status Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, <?php echo $card['status'] === 'active' ? '#10b981 0%, #059669 100%' : '#ef4444 0%, #dc2626 100%'; ?>);">
                    <i class="fas fa-<?php echo $card['status'] === 'active' ? 'check-circle' : 'times-circle'; ?>"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Card Status</div>
                    <div class="balance-amount" style="color: <?php echo $card['status'] === 'active' ? '#10b981' : '#ef4444'; ?>;">
                        <?php echo ucfirst($card['status']); ?>
                    </div>
                    <div class="balance-subtitle"><?php echo $card['status'] === 'active' ? 'Ready to Use' : 'Restricted'; ?></div>
                </div>
            </div>
        </div>



        <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
        </div>
        <?php endif; ?>

        <!-- Full Width Content Section -->
        <div class="full-width-content">
            <div class="content-container">
                <div class="row">
                <!-- Card Information -->
                <div class="col-lg-8">
                    <!-- Card Visual - Dashboard Style -->
                    <div class="card-display-full">
                        <div class="virtual-card-container">
                            <div class="virtual-card">
                                <div class="card-header">
                                    <div class="card-type">Virtual Card</div>
                                    <div class="card-logo">
                                        <i class="fas fa-university"></i>
                                    </div>
                                </div>
                                <div class="card-number">
                                    <span class="number-group">****</span>
                                    <span class="number-group">****</span>
                                    <span class="number-group">****</span>
                                    <span class="number-group"><?php echo substr($card['card_number'], -4); ?></span>
                                </div>
                                <div class="card-info">
                                    <div class="card-holder">
                                        <div class="label">CARD HOLDER</div>
                                        <div class="value"><?php echo strtoupper($card['card_holder_name']); ?></div>
                                    </div>
                                    <div class="card-expiry">
                                        <div class="label">VALID THRU</div>
                                        <div class="value"><?php echo date('m/y', strtotime($card['expiry_date'])); ?></div>
                                    </div>
                                </div>
                                <div class="card-status-overlay">
                                    <span class="status-badge status-<?php echo $card['status']; ?>">
                                        <?php echo ucfirst($card['status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Card Controls -->
                    <div class="card-controls-section">
                        <div class="controls-header">
                            <h3><i class="fas fa-sliders-h me-2"></i>Card Controls</h3>
                        </div>
                        <div class="controls-grid">
                            <!-- Freeze/Unfreeze Card -->
                            <div class="control-item">
                                <div class="control-info">
                                    <h4><?php echo $card['status'] === 'active' ? 'Freeze Card' : 'Unfreeze Card'; ?></h4>
                                    <p><?php echo $card['status'] === 'active' ? 'Temporarily block all transactions' : 'Reactivate card for transactions'; ?></p>
                                </div>
                                <div class="control-action">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="<?php echo $card['status'] === 'active' ? 'freeze_card' : 'unfreeze_card'; ?>">
                                        <button type="submit" class="btn <?php echo $card['status'] === 'active' ? 'btn-warning' : 'btn-success'; ?>" 
                                                onclick="return confirm('Are you sure you want to <?php echo $card['status'] === 'active' ? 'freeze' : 'unfreeze'; ?> this card?')">
                                            <i class="fas fa-<?php echo $card['status'] === 'active' ? 'snowflake' : 'play'; ?> me-2"></i>
                                            <?php echo $card['status'] === 'active' ? 'Freeze Card' : 'Unfreeze Card'; ?>
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- Update Limits -->
                            <div class="control-item">
                                <div class="control-info">
                                    <h4>Spending Limits</h4>
                                    <p>Adjust daily and monthly spending limits</p>
                                </div>
                                <div class="control-action">
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#limitsModal">
                                        <i class="fas fa-edit me-2"></i>Update Limits
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Transactions -->
                    <div class="transactions-section">
                        <div class="section-header">
                            <h3><i class="fas fa-history me-2"></i>Recent Transactions</h3>
                        </div>
                        <div class="transactions-list">
                            <?php if (empty($transactions)): ?>
                            <div class="no-transactions">
                                <i class="fas fa-receipt"></i>
                                <p>No transactions yet</p>
                                <small>Your card transactions will appear here</small>
                            </div>
                            <?php else: ?>
                            <?php foreach ($transactions as $transaction): ?>
                            <div class="transaction-item">
                                <div class="transaction-icon">
                                    <i class="fas fa-<?php echo $transaction['transaction_type'] === 'credit' ? 'plus' : 'minus'; ?>"></i>
                                </div>
                                <div class="transaction-details">
                                    <div class="transaction-description"><?php echo htmlspecialchars($transaction['description']); ?></div>
                                    <div class="transaction-date"><?php echo date('M j, Y g:i A', strtotime($transaction['created_at'])); ?></div>
                                </div>
                                <div class="transaction-amount <?php echo $transaction['transaction_type'] === 'credit' ? 'credit' : 'debit'; ?>">
                                    <?php echo $transaction['transaction_type'] === 'credit' ? '+' : '-'; ?>
                                    <?php echo formatCurrency($transaction['amount'], $transaction['currency'] ?? 'USD'); ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Card Stats -->
                <div class="col-lg-4">
                    <div class="card-stats-section">
                        <div class="stats-header">
                            <h4><i class="fas fa-chart-bar me-2"></i>Card Statistics</h4>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-label">Current Balance</div>
                                <div class="stat-value balance">
                                    <?php echo formatCurrency($card['card_balance'], $card['currency'] ?? 'USD'); ?>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Daily Limit</div>
                                <div class="stat-value">
                                    <?php echo formatCurrency($card['daily_limit'], $card['currency'] ?? 'USD'); ?>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Monthly Limit</div>
                                <div class="stat-value">
                                    <?php echo formatCurrency($card['monthly_limit'], $card['currency'] ?? 'USD'); ?>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Card Created</div>
                                <div class="stat-value">
                                    <?php echo date('M j, Y', strtotime($card['created_at'])); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions-section">
                        <div class="actions-header">
                            <h4><i class="fas fa-bolt me-2"></i>Quick Actions</h4>
                        </div>
                        <div class="actions-list">
                            <a href="../accounts/index.php" class="action-item">
                                <div class="action-icon">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <div class="action-info">
                                    <div class="action-title">Transfer Money</div>
                                    <div class="action-subtitle">Add funds to this card</div>
                                </div>
                            </a>
                            <a href="../transactions/index.php" class="action-item">
                                <div class="action-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                                <div class="action-info">
                                    <div class="action-title">View All Transactions</div>
                                    <div class="action-subtitle">Complete transaction history</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once __DIR__ . '/../shared/user_footer.php'; ?>
</div>

<!-- Limits Modal -->
<div class="modal fade" id="limitsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Spending Limits</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_limits">
                    <div class="form-group mb-3">
                        <label for="daily_limit">Daily Limit</label>
                        <input type="number" id="daily_limit" name="daily_limit" class="form-control"
                               min="100" max="10000" step="50" value="<?php echo $card['daily_limit']; ?>" required>
                        <div class="amount-suggestions mt-2">
                            <button type="button" class="amount-btn" onclick="setDailyLimit(500)">$500</button>
                            <button type="button" class="amount-btn" onclick="setDailyLimit(1000)">$1,000</button>
                            <button type="button" class="amount-btn" onclick="setDailyLimit(2500)">$2,500</button>
                            <button type="button" class="amount-btn" onclick="setDailyLimit(5000)">$5,000</button>
                        </div>
                        <small class="form-text text-muted">Daily limit: $<?php echo number_format($card['daily_limit']); ?> • Monthly estimate: $<?php echo number_format($card['monthly_limit']); ?></small>
                    </div>
                    <div class="form-group">
                        <label for="monthly_limit">Monthly Limit</label>
                        <input type="number" id="monthly_limit" name="monthly_limit" class="form-control"
                               min="100" max="50000" step="100" value="<?php echo $card['monthly_limit']; ?>" required>
                        <small class="form-text text-muted">Between daily limit - $50,000</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Limits</button>
                </div>
            </form>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once __DIR__ . '/../shared/user_footer.php'; ?>
</div>

<script src="manage-card.js"></script>
