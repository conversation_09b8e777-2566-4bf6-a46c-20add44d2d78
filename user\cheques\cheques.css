/**
 * Cheque Preview Page CSS
 * Styles for cheque book preview and management
 */

/* Import dashboard base styles */
@import url('../dashboard/dashboard.css');

/* Cheque Preview Specific Styles */
.cheque-preview {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.cheque-container {
    background: white;
    border: 2px solid #333;
    border-radius: 8px;
    padding: 1.5rem;
    font-family: 'Courier New', monospace;
    max-width: 600px;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cheque-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #ddd;
}

.bank-info h4 {
    margin: 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.2rem;
}

.bank-info p {
    margin: 0.25rem 0;
    font-size: 0.8rem;
    color: #666;
}

.cheque-number {
    text-align: right;
    font-size: 0.9rem;
}

.cheque-body {
    margin-bottom: 1.5rem;
}

.date-section,
.pay-to-section,
.memo-section {
    margin-bottom: 1rem;
}

.amount-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
}

.amount-words {
    flex: 1;
}

.amount-box {
    min-width: 120px;
    text-align: right;
}

.signature-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: 2rem;
}

.account-info {
    font-size: 0.8rem;
    color: #666;
}

.account-info p {
    margin: 0.25rem 0;
}

.signature-line {
    text-align: center;
}

.signature-line label {
    display: block;
    margin-bottom: 0.25rem;
}

.signature-line p {
    margin: 0;
    font-size: 0.7rem;
    color: #666;
}

.cheque-footer {
    border-top: 1px solid #ddd;
    padding-top: 0.5rem;
    text-align: center;
}

.routing-info {
    font-family: 'MICR Encoding', 'Courier New', monospace;
    font-size: 0.9rem;
    color: #333;
    letter-spacing: 1px;
}

/* Information Cards */
.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.info-item span {
    color: #1f2937;
    font-weight: 500;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6b7280;
}

.empty-state h5 {
    color: #374151;
    margin-bottom: 1rem;
}

.empty-state p {
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Action Buttons */
.btn-outline-primary:hover,
.btn-outline-secondary:hover,
.btn-outline-info:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .cheque-preview {
        padding: 1rem;
    }
    
    .cheque-container {
        padding: 1rem;
        font-size: 0.85rem;
    }
    
    .cheque-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .amount-section {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .signature-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Print Styles */
@media print {
    .main-content {
        margin-left: 0;
    }
    
    .hero-section,
    .stats-section,
    .card-header,
    .col-lg-4,
    .btn {
        display: none !important;
    }
    
    .cheque-container {
        border: 2px solid #000;
        box-shadow: none;
        page-break-inside: avoid;
    }
    
    .cheque-preview {
        background: white;
        padding: 0;
    }
}

/* Animation for loading */
.cheque-container {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
