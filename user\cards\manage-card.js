/**
 * Manage Card Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Manage card page loaded');
    initializeManageCard();
});

/**
 * Initialize manage card functionality
 */
function initializeManageCard() {
    // Add event listeners for limit validation
    const dailyLimitInput = document.getElementById('daily_limit');
    const monthlyLimitInput = document.getElementById('monthly_limit');

    if (dailyLimitInput && monthlyLimitInput) {
        dailyLimitInput.addEventListener('input', validateLimits);
        monthlyLimitInput.addEventListener('input', validateLimits);
    }

    // Add confirmation for freeze/unfreeze actions
    const freezeButtons = document.querySelectorAll('button[onclick*="confirm"]');
    freezeButtons.forEach(button => {
        button.addEventListener('click', handleFreezeConfirmation);
    });
}

/**
 * Set daily limit from amount buttons
 */
function setDailyLimit(amount) {
    const dailyLimitInput = document.getElementById('daily_limit');
    if (dailyLimitInput) {
        dailyLimitInput.value = amount;

        // Auto-adjust monthly limit if needed
        const monthlyLimitInput = document.getElementById('monthly_limit');
        if (monthlyLimitInput) {
            const currentMonthly = parseInt(monthlyLimitInput.value);
            const suggestedMonthly = amount * 30; // 30 days estimate

            if (currentMonthly < amount || currentMonthly < suggestedMonthly) {
                monthlyLimitInput.value = Math.min(suggestedMonthly, 50000);
            }
        }

        validateLimits();
    }
}

/**
 * Validate spending limits
 */
function validateLimits() {
    const dailyLimit = parseFloat(document.getElementById('daily_limit').value) || 0;
    const monthlyLimit = parseFloat(document.getElementById('monthly_limit').value) || 0;
    
    const dailyInput = document.getElementById('daily_limit');
    const monthlyInput = document.getElementById('monthly_limit');
    
    // Validate daily limit
    if (dailyLimit < 100) {
        dailyInput.setCustomValidity('Daily limit must be at least $100');
    } else if (dailyLimit > 10000) {
        dailyInput.setCustomValidity('Daily limit cannot exceed $10,000');
    } else {
        dailyInput.setCustomValidity('');
    }
    
    // Validate monthly limit
    if (monthlyLimit < dailyLimit) {
        monthlyInput.setCustomValidity('Monthly limit must be at least equal to daily limit');
    } else if (monthlyLimit > 50000) {
        monthlyInput.setCustomValidity('Monthly limit cannot exceed $50,000');
    } else {
        monthlyInput.setCustomValidity('');
    }
    
    // Update visual feedback
    updateLimitFeedback(dailyLimit, monthlyLimit);
}

/**
 * Update limit feedback display
 */
function updateLimitFeedback(dailyLimit, monthlyLimit) {
    // You can add visual feedback here if needed
    const dailyInput = document.getElementById('daily_limit');
    const monthlyInput = document.getElementById('monthly_limit');
    
    // Remove existing classes
    dailyInput.classList.remove('is-valid', 'is-invalid');
    monthlyInput.classList.remove('is-valid', 'is-invalid');
    
    // Add appropriate classes
    if (dailyLimit >= 100 && dailyLimit <= 10000) {
        dailyInput.classList.add('is-valid');
    } else if (dailyLimit > 0) {
        dailyInput.classList.add('is-invalid');
    }
    
    if (monthlyLimit >= dailyLimit && monthlyLimit <= 50000) {
        monthlyInput.classList.add('is-valid');
    } else if (monthlyLimit > 0) {
        monthlyInput.classList.add('is-invalid');
    }
}

/**
 * Handle freeze confirmation
 */
function handleFreezeConfirmation(event) {
    const button = event.target.closest('button');
    const action = button.querySelector('i').classList.contains('fa-snowflake') ? 'freeze' : 'unfreeze';
    
    const message = action === 'freeze' 
        ? 'Are you sure you want to freeze this card? All transactions will be blocked until you unfreeze it.'
        : 'Are you sure you want to unfreeze this card? Transactions will be enabled again.';
    
    if (!confirm(message)) {
        event.preventDefault();
        return false;
    }
    
    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    button.disabled = true;
    
    // The form will submit, but if there's an error, restore the button
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 5000);
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 500px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

/**
 * Format currency display
 */
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * Copy card number to clipboard
 */
function copyCardNumber() {
    const cardNumber = document.querySelector('.card-number').textContent.replace(/\s/g, '');
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(cardNumber).then(() => {
            showNotification('Card number copied to clipboard!', 'success');
        }).catch(() => {
            showNotification('Failed to copy card number', 'error');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = cardNumber;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showNotification('Card number copied to clipboard!', 'success');
        } catch (err) {
            showNotification('Failed to copy card number', 'error');
        }
        document.body.removeChild(textArea);
    }
}

/**
 * Add click handler for card number copying
 */
document.addEventListener('DOMContentLoaded', function() {
    const cardNumber = document.querySelector('.card-number');
    if (cardNumber) {
        cardNumber.style.cursor = 'pointer';
        cardNumber.title = 'Click to copy card number';
        cardNumber.addEventListener('click', copyCardNumber);
    }
});

/**
 * Add CSS for form validation feedback
 */
const style = document.createElement('style');
style.textContent = `
    .form-control.is-valid {
        border-color: #28a745;
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    }
    
    .form-control.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }
    
    .notification-toast {
        animation: slideIn 0.3s ease;
    }
    
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .card-number:hover {
        opacity: 0.8;
        transform: scale(1.02);
        transition: all 0.3s ease;
    }
    
    .transaction-item:hover .transaction-icon {
        transform: scale(1.1);
        transition: transform 0.3s ease;
    }
    
    .action-item:hover .action-icon {
        transform: scale(1.1);
        transition: transform 0.3s ease;
    }
`;
document.head.appendChild(style);
