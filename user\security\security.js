/**
 * Security Settings Page JavaScript
 */

// Show password change form
function showPasswordChangeForm() {
    // Create modal for password change
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'passwordChangeModal';
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="far fa-key me-2"></i>Change Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="changePasswordForm">
                        <div class="mb-3">
                            <label for="currentPassword" class="form-label">Current Password</label>
                            <div class="password-input-group">
                                <input type="password" class="form-control" id="currentPassword" name="current_password" required>
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('currentPassword')">
                                    <i class="far fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">New Password</label>
                            <div class="password-input-group">
                                <input type="password" class="form-control" id="newPassword" name="new_password" required>
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('newPassword')">
                                    <i class="far fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-requirements mt-2">
                                <small class="text-muted">
                                    <i class="far fa-info-circle me-1"></i>
                                    Password must be at least 8 characters with uppercase, lowercase, number, and special character.
                                </small>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm New Password</label>
                            <div class="password-input-group">
                                <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('confirmPassword')">
                                    <i class="far fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="far fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" onclick="submitPasswordChange()">
                        <i class="far fa-save me-1"></i>Update Password
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    // Clean up modal when hidden
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// Toggle password visibility
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'far fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'far fa-eye';
    }
}

// Submit password change
function submitPasswordChange() {
    const form = document.getElementById('changePasswordForm');
    const formData = new FormData(form);
    
    // Validate passwords match
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    if (newPassword !== confirmPassword) {
        showAlert('error', 'New passwords do not match.');
        return;
    }
    
    // Validate password strength
    if (!validatePasswordStrength(newPassword)) {
        showAlert('error', 'Password does not meet security requirements.');
        return;
    }
    
    // Show loading state
    const submitBtn = document.querySelector('#passwordChangeModal .btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
    submitBtn.disabled = true;
    
    // Submit form via AJAX
    fetch('change-password.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Password updated successfully!');
            bootstrap.Modal.getInstance(document.getElementById('passwordChangeModal')).hide();
        } else {
            showAlert('error', data.message || 'Failed to update password.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while updating password.');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Validate password strength
function validatePasswordStrength(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
}

// Toggle 2FA
function toggle2FA() {
    showAlert('info', 'Two-Factor Authentication setup is coming soon. Please contact support for assistance.');
}

// Toggle OTP
function toggleOTP() {
    showAlert('info', 'OTP settings can be managed by contacting our support team.');
}

// Show alert messages
function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert-notification');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alert = document.createElement('div');
    alert.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show alert-notification`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';
    
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(alert);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add any initialization code here
    console.log('Security Settings page loaded');
});

// Password input group styling
const style = document.createElement('style');
style.textContent = `
    .password-input-group {
        position: relative;
    }
    
    .password-input-group .form-control {
        padding-right: 45px;
    }
    
    .password-toggle {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        padding: 5px;
        z-index: 10;
    }
    
    .password-toggle:hover {
        color: var(--primary-color);
    }
    
    .password-requirements {
        font-size: 0.875rem;
    }
`;
document.head.appendChild(style);
