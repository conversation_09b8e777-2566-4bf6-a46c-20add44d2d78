<?php
/**
 * Apply New Features Database Schema
 * Run this script to create the new database tables for IRS, Crypto, and Card features
 */

require_once 'config/config.php';

try {
    $db = getDB();
    
    // Read the schema file
    $schema_file = __DIR__ . '/sql/new_features_schema.sql';
    if (!file_exists($schema_file)) {
        throw new Exception("Schema file not found: $schema_file");
    }
    
    $sql_content = file_get_contents($schema_file);
    if ($sql_content === false) {
        throw new Exception("Failed to read schema file");
    }
    
    // Split the SQL content into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt) && !preg_match('/^\s*\/\*/', $stmt);
        }
    );
    
    echo "Applying database schema...\n";
    echo "Found " . count($statements) . " SQL statements to execute.\n\n";
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($statements as $index => $statement) {
        $statement = trim($statement);
        if (empty($statement)) continue;
        
        try {
            // Execute the statement
            $result = $db->query($statement);
            
            // Determine what type of statement this was
            $stmt_type = 'UNKNOWN';
            if (preg_match('/^\s*DROP\s+TABLE/i', $statement)) {
                $stmt_type = 'DROP TABLE';
            } elseif (preg_match('/^\s*CREATE\s+TABLE\s+`?(\w+)`?/i', $statement, $matches)) {
                $stmt_type = 'CREATE TABLE ' . $matches[1];
            } elseif (preg_match('/^\s*INSERT\s+INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                $stmt_type = 'INSERT INTO ' . $matches[1];
            } elseif (preg_match('/^\s*ALTER\s+TABLE/i', $statement)) {
                $stmt_type = 'ALTER TABLE';
            }
            
            echo "✓ " . ($index + 1) . ". $stmt_type - SUCCESS\n";
            $success_count++;
            
        } catch (Exception $e) {
            echo "✗ " . ($index + 1) . ". Statement failed: " . $e->getMessage() . "\n";
            echo "   SQL: " . substr($statement, 0, 100) . "...\n";
            $error_count++;
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Schema application completed!\n";
    echo "Successful statements: $success_count\n";
    echo "Failed statements: $error_count\n";
    
    if ($error_count === 0) {
        echo "\n🎉 All database tables created successfully!\n";
        echo "You can now use the new features:\n";
        echo "- IRS Tax Return Assistance\n";
        echo "- Crypto Transfers (Simulation)\n";
        echo "- Crypto Deposits\n";
        echo "- Virtual Card Top-ups\n";
    } else {
        echo "\n⚠️  Some statements failed. Please check the errors above.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
