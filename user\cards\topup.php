<?php
/**
 * Virtual Card Top-up
 * Allow users to top up their virtual cards using various payment methods
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page variables
$page_title = 'Card Top-up';
$current_page = 'topup';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Check if card top-ups feature is enabled
$topup_enabled = '1'; // Default to enabled
try {
    $topup_enabled_query = "SELECT value FROM admin_settings WHERE setting_key = 'card_topups_enabled'";
    $topup_enabled_result = $db->query($topup_enabled_query);
    if ($topup_enabled_result && $row = $topup_enabled_result->fetch_assoc()) {
        $topup_enabled = $row['value'];
    }
} catch (Exception $e) {
    // If table doesn't exist, default to enabled
    error_log("Card topup feature check error: " . $e->getMessage());
}

if ($topup_enabled !== '1') {
    header('Location: ../dashboard/index.php');
    exit();
}

// Get user's virtual cards
$user_cards = [];
try {
    $cards_query = "SELECT * FROM virtual_cards WHERE account_id = ? AND status = 'active' ORDER BY created_at DESC";
    $cards_result = $db->query($cards_query, [$user_id]);
    if ($cards_result) {
        while ($row = $cards_result->fetch_assoc()) {
            $user_cards[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Virtual cards query error: " . $e->getMessage());
    // Continue with empty array
}

// Get available payment methods
$payment_methods = [];
try {
    $payment_methods_query = "SELECT * FROM payment_methods WHERE is_active = 1 ORDER BY method_name";
    $payment_methods_result = $db->query($payment_methods_query);
    if ($payment_methods_result) {
        while ($row = $payment_methods_result->fetch_assoc()) {
            $payment_methods[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Payment methods query error: " . $e->getMessage());
    // Continue with empty array
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $card_id = intval($_POST['card_id']);
        $payment_method_id = intval($_POST['payment_method_id']);
        $amount = floatval($_POST['amount']);
        $topup_note = trim($_POST['topup_note'] ?? '');
        
        // Validation
        if (empty($card_id) || empty($payment_method_id) || $amount <= 0) {
            throw new Exception("Please fill in all required fields with valid values.");
        }
        
        // Validate amount limits
        if ($amount < 10) {
            throw new Exception("Minimum top-up amount is $10.");
        }
        if ($amount > 5000) {
            throw new Exception("Maximum top-up amount is $5,000 per transaction.");
        }
        
        // Verify card belongs to user
        $card_check_query = "SELECT * FROM virtual_cards WHERE card_id = ? AND account_id = ? AND status = 'active'";
        $card_check_result = $db->query($card_check_query, [$card_id, $user_id]);
        $selected_card = $card_check_result->fetch_assoc();
        
        if (!$selected_card) {
            throw new Exception("Invalid card selected or card is not active.");
        }
        
        // Verify payment method is active
        $payment_check_query = "SELECT * FROM payment_methods WHERE id = ? AND is_active = 1";
        $payment_check_result = $db->query($payment_check_query, [$payment_method_id]);
        $selected_payment_method = $payment_check_result->fetch_assoc();
        
        if (!$selected_payment_method) {
            throw new Exception("Invalid payment method selected.");
        }
        
        // Generate transaction reference
        $transaction_reference = 'TOP_' . strtoupper(bin2hex(random_bytes(8)));
        
        // Insert card top-up request
        $insert_query = "INSERT INTO card_topups (
            account_id, card_id, payment_method_id, topup_amount,
            transaction_reference, user_notes, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())";

        $topup_id = $db->insert($insert_query, [
            $user_id, $card_id, $payment_method_id, $amount,
            $transaction_reference, $topup_note
        ]);
        
        if ($topup_id) {
            $success_message = "Card top-up request submitted successfully! Reference: " . $transaction_reference;
        } else {
            throw new Exception("Failed to submit top-up request. Please try again.");
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get recent top-ups for user
$recent_topups = [];
try {
    $recent_topups_query = "SELECT ct.*, vc.card_number, vc.card_holder_name as card_name, pm.method_name
                           FROM card_topups ct
                           LEFT JOIN virtual_cards vc ON ct.card_id = vc.card_id
                           LEFT JOIN payment_methods pm ON ct.payment_method_id = pm.id
                           WHERE ct.account_id = ?
                           ORDER BY ct.created_at DESC LIMIT 5";
    $recent_topups_result = $db->query($recent_topups_query, [$user_id]);
    if ($recent_topups_result) {
        while ($row = $recent_topups_result->fetch_assoc()) {
            $recent_topups[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Recent topups query error: " . $e->getMessage());
    // Continue with empty array
}
// Set page title
$page_title = 'Card Top-up';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Cards CSS -->
<link rel="stylesheet" href="cards.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content">

        <!-- Card Top-up Hero Section -->
        <div class="cards-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Virtual Card Top-up</div>
                    <div class="hero-subtitle">Add funds to your virtual cards</div>
                    <div class="hero-stats">
                        Available Cards: <?php echo count($user_cards); ?> • Payment Methods: <?php echo count($payment_methods); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="index.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-credit-card me-2"></i>My Cards
                    </a>
                    <a href="manage-card.php" class="btn btn-primary">
                        <i class="fas fa-cog me-2"></i>Manage Cards
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="cards-overview">
            <div class="overview-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo count($user_cards); ?></div>
                        <div class="stat-label">Available Cards</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo count($payment_methods); ?></div>
                        <div class="stat-label">Payment Methods</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo count($recent_topups); ?></div>
                        <div class="stat-label">Recent Top-ups</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Top-up Form -->
        <div class="row">
            <div class="col-lg-8">
                    <div class="card crypto-card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-plus me-2"></i>
                                New Card Top-up
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($user_cards)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Active Cards</h5>
                                <p class="text-muted">You don't have any active virtual cards to top up.</p>
                                <a href="index.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Request a Virtual Card
                                </a>
                            </div>
                            <?php else: ?>
                            <form method="POST" action="" id="cardTopupForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="card_id" class="form-label">Select Card</label>
                                            <select class="form-select" id="card_id" name="card_id" required>
                                                <option value="">Choose a card to top up</option>
                                                <?php foreach ($user_cards as $card): ?>
                                                <option value="<?php echo $card['card_id']; ?>">
                                                    <?php echo htmlspecialchars($card['card_holder_name']); ?>
                                                    (**** <?php echo substr($card['card_number'], -4); ?>)
                                                    - Balance: $<?php echo number_format($card['card_balance'], 2); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Top-up Amount</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="amount" name="amount" 
                                                       step="0.01" min="10" max="5000" required>
                                            </div>
                                            <div class="form-text">Minimum: $10, Maximum: $5,000 per transaction</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Amount Buttons -->
                                <div class="mb-3">
                                    <label class="form-label">Quick Amounts</label>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-primary" onclick="setAmount(50)">$50</button>
                                        <button type="button" class="btn btn-outline-primary" onclick="setAmount(100)">$100</button>
                                        <button type="button" class="btn btn-outline-primary" onclick="setAmount(250)">$250</button>
                                        <button type="button" class="btn btn-outline-primary" onclick="setAmount(500)">$500</button>
                                        <button type="button" class="btn btn-outline-primary" onclick="setAmount(1000)">$1,000</button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="payment_method_id" class="form-label">Payment Method</label>
                                    <select class="form-select" id="payment_method_id" name="payment_method_id" required>
                                        <option value="">Select Payment Method</option>
                                        <?php foreach ($payment_methods as $method): ?>
                                        <option value="<?php echo $method['id']; ?>">
                                            <?php echo htmlspecialchars($method['method_name']); ?>
                                            <?php if ($method['processing_fee'] > 0): ?>
                                            (Fee: <?php echo $method['fee_type'] === 'percentage' ? $method['processing_fee'] . '%' : '$' . number_format($method['processing_fee'], 2); ?>)
                                            <?php endif; ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="topup_note" class="form-label">Note (Optional)</label>
                                    <textarea class="form-control" id="topup_note" name="topup_note" rows="3" 
                                              placeholder="Add a note about this top-up (optional)"></textarea>
                                </div>



                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>Reset Form
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Submit Top-up Request
                                    </button>
                                </div>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Top-ups Sidebar -->
                <div class="col-lg-4">
                    <div class="card crypto-card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Top-ups
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_topups)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-3"></i>
                                <p>No recent top-ups</p>
                            </div>
                            <?php else: ?>
                            <div class="transfer-list">
                                <?php foreach ($recent_topups as $topup): ?>
                                <div class="transfer-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="fw-bold">$<?php echo number_format($topup['topup_amount'], 2); ?></div>
                                            <div class="text-muted small"><?php echo date('M j, Y', strtotime($topup['created_at'])); ?></div>
                                        </div>
                                        <span class="badge bg-<?php echo $topup['status'] === 'approved' ? 'success' : ($topup['status'] === 'declined' ? 'danger' : 'warning'); ?>">
                                            <?php echo ucfirst($topup['status']); ?>
                                        </span>
                                    </div>
                                    <div class="text-muted small mt-1">
                                        Card: **** <?php echo substr($topup['card_number'], -4); ?>
                                    </div>
                                    <div class="text-muted small">
                                        Method: <?php echo htmlspecialchars($topup['method_name']); ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<style>
.cards-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 2rem;
    color: white;
    margin-bottom: 2rem;
}

.cards-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.cards-hero .hero-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.cards-hero .hero-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.cards-hero .hero-stats {
    font-size: 0.95rem;
    opacity: 0.8;
}

.cards-overview {
    margin-bottom: 2rem;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}
</style>

<script>
    function setAmount(amount) {
        document.getElementById('amount').value = amount;
    }

    function resetForm() {
        document.getElementById('cardTopupForm').reset();
    }

    // Auto-dismiss alerts after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });
    });
</script>
