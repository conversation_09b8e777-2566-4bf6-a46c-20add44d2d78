<?php
require_once '../../config/config.php';
requireLogin();

$page_title = 'Request New Virtual Card';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../auth/login.php');
    exit();
}

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $card_type = trim($_POST['card_type']);
        $requested_limit = floatval($_POST['requested_limit']);
        $purpose = trim($_POST['purpose']);
        
        // Validate inputs
        if (!in_array($card_type, ['visa', 'mastercard'])) {
            throw new Exception("Please select a valid card type.");
        }
        
        if ($requested_limit < 100 || $requested_limit > 10000) {
            throw new Exception("Daily limit must be between $100 and $10,000.");
        }
        
        if (empty($purpose)) {
            throw new Exception("Please specify the purpose for this card.");
        }
        
        // Check if user has pending applications
        $pending_check = "SELECT COUNT(*) as pending_count FROM card_applications 
                         WHERE account_id = ? AND status = 'pending'";
        $pending_result = $db->query($pending_check, [$user_id]);
        $pending_data = $pending_result->fetch_assoc();
        
        if ($pending_data['pending_count'] > 0) {
            throw new Exception("You already have a pending card application. Please wait for approval.");
        }
        
        // Create card application
        $insert_application = "INSERT INTO card_applications (
            account_id, application_type, requested_limit, purpose,
            status, applied_at
        ) VALUES (?, ?, ?, ?, 'pending', NOW())";

        $application_id = $db->insert($insert_application, [
            $user_id, $card_type, $requested_limit, $purpose
        ]);

        if ($application_id) {
            $success = "Your card application has been submitted successfully! Application ID: #$application_id. You will be notified once it's reviewed.";
        } else {
            throw new Exception("Failed to submit application. Please try again.");
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get user's existing cards count
$cards_count_query = "SELECT COUNT(*) as card_count FROM virtual_cards WHERE account_id = ?";
$cards_count_result = $db->query($cards_count_query, [$user_id]);
$cards_count = $cards_count_result->fetch_assoc()['card_count'];

// Get pending applications
$pending_apps_query = "SELECT * FROM card_applications WHERE account_id = ? AND status = 'pending' ORDER BY applied_at DESC";
$pending_apps_result = $db->query($pending_apps_query, [$user_id]);
$pending_applications = [];
while ($app = $pending_apps_result->fetch_assoc()) {
    $pending_applications[] = $app;
}

require_once '../shared/header.php';
?>

<link rel="stylesheet" href="../dashboard/dashboard.css">
<link rel="stylesheet" href="cards.css">
<link rel="stylesheet" href="request-card.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content">
        <!-- Cards Hero Section -->
        <div class="cards-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Request New Virtual Card</div>
                    <div class="hero-subtitle">Apply for a new virtual card for online purchases and secure transactions</div>
                    <div class="hero-stats">
                        Current Cards: <?php echo $cards_count; ?> • Pending Applications: <?php echo count($pending_applications); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="index.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Cards
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Current Cards Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Current Cards</div>
                    <div class="balance-amount available">
                        <?php echo $cards_count; ?>
                    </div>
                    <div class="balance-subtitle">Active Cards</div>
                </div>
            </div>

            <!-- Pending Applications Card -->
            <div class="balance-card-new">
                <div class="balance-icon card">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Pending</div>
                    <div class="balance-amount card">
                        <?php echo count($pending_applications); ?>
                    </div>
                    <div class="balance-subtitle">Applications</div>
                </div>
            </div>

            <!-- Processing Time Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Processing</div>
                    <div class="balance-amount" style="color: #8b5cf6;">
                        1-2 Days
                    </div>
                    <div class="balance-subtitle">Business Days</div>
                </div>
            </div>

            <!-- Account Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Account Balance</div>
                    <div class="balance-amount available">
                        <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                    </div>
                    <div class="balance-subtitle">Available Funds</div>
                </div>
            </div>
        </div>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
        </div>
        <?php endif; ?>

        <!-- Full Width Content Section -->
        <div class="full-width-content">
            <div class="content-container">
                <div class="row">
                    <div class="col-lg-8">
                    <div class="request-form-card">
                        <div class="card-header">
                            <h3><i class="fas fa-plus-circle me-2"></i>New Card Application</h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="card-request-form">
                                <div class="form-section">
                                    <h4>Card Type</h4>
                                    <div class="card-type-selection">
                                        <div class="card-type-option">
                                            <input type="radio" id="visa" name="card_type" value="visa" required>
                                            <label for="visa" class="card-type-label">
                                                <div class="card-type-visual visa">
                                                    <svg width="48" height="32" viewBox="0 0 48 32" fill="none">
                                                        <rect width="48" height="32" rx="4" fill="#1a1f71"/>
                                                        <text x="24" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">VISA</text>
                                                    </svg>
                                                    <span>Visa</span>
                                                </div>
                                                <div class="card-type-info">
                                                    <p>Widely accepted worldwide</p>
                                                    <small>Perfect for online shopping and international transactions</small>
                                                </div>
                                            </label>
                                        </div>
                                        <div class="card-type-option">
                                            <input type="radio" id="mastercard" name="card_type" value="mastercard" required>
                                            <label for="mastercard" class="card-type-label">
                                                <div class="card-type-visual mastercard">
                                                    <svg width="48" height="32" viewBox="0 0 48 32" fill="none">
                                                        <rect width="48" height="32" rx="4" fill="#eb001b"/>
                                                        <circle cx="18" cy="16" r="8" fill="#ff5f00" opacity="0.8"/>
                                                        <circle cx="30" cy="16" r="8" fill="#f79e1b" opacity="0.8"/>
                                                    </svg>
                                                    <span>Mastercard</span>
                                                </div>
                                                <div class="card-type-info">
                                                    <p>Global acceptance and security</p>
                                                    <small>Excellent for both online and offline purchases</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>Daily Spending Limit</h4>
                                    <div class="limit-selection">
                                        <div class="form-group">
                                            <label for="requested_limit">Daily Limit (USD)</label>
                                            <input type="number" id="requested_limit" name="requested_limit" 
                                                   class="form-control" min="100" max="10000" step="50" 
                                                   value="1000" required>
                                            <div class="limit-info">
                                                <small>Choose between $100 - $10,000 daily spending limit</small>
                                            </div>
                                        </div>
                                        <div class="limit-suggestions">
                                            <button type="button" class="limit-btn" onclick="setLimit(500)">$500</button>
                                            <button type="button" class="limit-btn" onclick="setLimit(1000)">$1,000</button>
                                            <button type="button" class="limit-btn" onclick="setLimit(2500)">$2,500</button>
                                            <button type="button" class="limit-btn" onclick="setLimit(5000)">$5,000</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>Purpose</h4>
                                    <div class="form-group">
                                        <label for="purpose">What will you use this card for?</label>
                                        <select id="purpose" name="purpose" class="form-control" required>
                                            <option value="">Select purpose</option>
                                            <option value="online_shopping">Online Shopping</option>
                                            <option value="subscriptions">Subscriptions & Recurring Payments</option>
                                            <option value="business_expenses">Business Expenses</option>
                                            <option value="travel_bookings">Travel & Hotel Bookings</option>
                                            <option value="digital_services">Digital Services & Software</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>Submit Application
                                    </button>
                                    <a href="index.php" class="btn btn-outline-secondary btn-lg">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Application Status -->
                    <div class="status-card">
                        <div class="card-header">
                            <h4><i class="fas fa-info-circle me-2"></i>Application Info</h4>
                        </div>
                        <div class="card-body">
                            <div class="status-item">
                                <label>Current Cards</label>
                                <div class="status-value"><?php echo $cards_count; ?> Active</div>
                            </div>
                            <div class="status-item">
                                <label>Pending Applications</label>
                                <div class="status-value"><?php echo count($pending_applications); ?></div>
                            </div>
                            <div class="status-item">
                                <label>Processing Time</label>
                                <div class="status-value">1-2 Business Days</div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($pending_applications)): ?>
                    <!-- Pending Applications -->
                    <div class="pending-apps-card">
                        <div class="card-header">
                            <h4><i class="fas fa-clock me-2"></i>Pending Applications</h4>
                        </div>
                        <div class="card-body">
                            <?php foreach ($pending_applications as $app): ?>
                            <div class="pending-app-item">
                                <div class="app-id"><?php echo $app['application_id']; ?></div>
                                <div class="app-details">
                                    <span class="app-type"><?php echo ucfirst($app['card_type']); ?></span>
                                    <span class="app-limit">$<?php echo number_format($app['requested_limit']); ?> limit</span>
                                </div>
                                <div class="app-date"><?php echo date('M j, Y', strtotime($app['applied_at'])); ?></div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        </div> <!-- End Content Container -->

    </div> <!-- End Main Content -->

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<script src="request-card.js"></script>
