<?php
/**
 * Layout Test Page
 * Test the footer layout fixes
 */

// Set page variables
$page_title = 'Layout Test';
$current_page = 'test';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../auth/login.php');
    exit();
}

// Set page title
$page_title = 'Layout Test';

// Include header
require_once __DIR__ . '/shared/header.php';
?>

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
    
    .test-content {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        border: 1px solid var(--border-color);
        margin-bottom: 2rem;
    }
    
    .layout-info {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>

<!-- Sidebar -->
<?php require_once __DIR__ . '/shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once __DIR__ . '/shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Test Hero Section -->
        <div class="test-hero mb-4" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); border-radius: 12px; padding: 2rem; color: white;">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title" style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem;">Layout Test Page</div>
                    <div class="hero-subtitle" style="font-size: 1.125rem; opacity: 0.9;">Testing footer layout fixes and container structure</div>
                </div>
            </div>
        </div>

        <!-- Test Content -->
        <div class="test-content">
            <h3>Layout Test Results</h3>
            <p>This page tests the footer layout fixes implemented to resolve the content shifting issue.</p>
            
            <div class="layout-info">
                <h5>Expected Layout:</h5>
                <ul>
                    <li>Sidebar: Fixed at 280px width on the left</li>
                    <li>Main content: Should start after sidebar (margin-left: 280px on wrapper)</li>
                    <li>Footer: Should span full width of content area, not push content left</li>
                    <li>No double margins or layout shifts</li>
                </ul>
            </div>
            
            <div class="layout-info">
                <h5>CSS Fixes Applied:</h5>
                <ul>
                    <li>Override conflicting margin-left styles on .main-content</li>
                    <li>Ensure .main-content-wrapper has correct layout properties</li>
                    <li>Fix responsive behavior for mobile devices</li>
                    <li>Proper flexbox layout for footer positioning</li>
                </ul>
            </div>
            
            <h4>Test Content</h4>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="test-content">
                        <h5>Left Column</h5>
                        <p>This content should align properly with the layout.</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="test-content">
                        <h5>Right Column</h5>
                        <p>This content should also align properly.</p>
                    </div>
                </div>
            </div>
        </div>

        </div> <!-- End Content Container -->

    </div> <!-- End Main Content -->

    <!-- User Footer Component -->
    <?php require_once __DIR__ . '/shared/user_footer.php'; ?>
</div>

<script>
console.log('Layout Test Page Loaded');
console.log('Main Content Wrapper Width:', document.querySelector('.main-content-wrapper').offsetWidth);
console.log('Main Content Width:', document.querySelector('.main-content').offsetWidth);
console.log('Footer Width:', document.querySelector('.user-dashboard-footer').offsetWidth);
</script>
