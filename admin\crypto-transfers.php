<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Crypto Transfers Management';

// Handle transfer approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        $transfer_id = intval($_POST['transfer_id']);
        $action = $_POST['action'];
        $admin_notes = trim($_POST['admin_notes'] ?? '');
        
        if (!in_array($action, ['approve', 'decline'])) {
            throw new Exception("Invalid action.");
        }
        
        // Get transfer details
        $transfer_query = "SELECT ct.*, a.first_name, a.last_name, a.email 
                          FROM crypto_transfers ct 
                          LEFT JOIN accounts a ON ct.account_id = a.id 
                          WHERE ct.id = ? AND ct.status = 'pending'";
        $transfer_result = $db->query($transfer_query, [$transfer_id]);
        $transfer = $transfer_result->fetch_assoc();
        
        if (!$transfer) {
            throw new Exception("Transfer not found or already processed.");
        }
        
        $new_status = $action === 'approve' ? 'approved' : 'declined';
        
        // Update transfer status
        $update_query = "UPDATE crypto_transfers SET 
                        status = ?, admin_notes = ?, reviewed_by = ?, reviewed_at = NOW() 
                        WHERE id = ?";
        $db->query($update_query, [$new_status, $admin_notes, $_SESSION['user_id'], $transfer_id]);
        
        // Send email notification (placeholder for now)
        $email_subject = $action === 'approve' ? 
            'Crypto Transfer Approved' : 
            'Crypto Transfer Update';
            
        $email_body = $action === 'approve' ?
            "Dear {$transfer['first_name']},\n\nYour cryptocurrency transfer of {$transfer['amount']} {$transfer['crypto_type']} has been approved and processed.\n\nTransaction Hash: {$transfer['transaction_hash']}\n\nThank you for using our services." :
            "Dear {$transfer['first_name']},\n\nWe have reviewed your cryptocurrency transfer request. Unfortunately, we are unable to process your transfer at this time.\n\nReason: {$admin_notes}\n\nIf you have any questions, please contact our support team.";
        
        // Log email notification
        $email_insert = "INSERT INTO email_notifications (
            recipient_id, email_address, subject, message_body, notification_type, 
            related_record_id, related_record_type, status
        ) VALUES (?, ?, ?, ?, ?, ?, 'crypto_transfer', 'pending')";
        
        $db->query($email_insert, [
            $transfer['account_id'],
            $transfer['email'],
            $email_subject,
            $email_body,
            $action === 'approve' ? 'crypto_approval' : 'crypto_decline',
            $transfer_id
        ]);
        
        $success = "Transfer " . ($action === 'approve' ? 'approved' : 'declined') . " successfully!";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Pagination and filtering
$records_per_page = 20;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $records_per_page;

$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$crypto_filter = isset($_GET['crypto']) ? $_GET['crypto'] : '';

// Build WHERE clause
$where_conditions = [];
$params = [];

if (!empty($status_filter)) {
    $where_conditions[] = "ct.status = ?";
    $params[] = $status_filter;
}

if (!empty($crypto_filter)) {
    $where_conditions[] = "ct.crypto_type = ?";
    $params[] = $crypto_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM crypto_transfers ct $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get transfers
    $transfers_query = "SELECT ct.*, 
                       a.first_name, a.last_name, a.username, a.account_number,
                       admin.first_name as admin_first_name, admin.last_name as admin_last_name
                       FROM crypto_transfers ct 
                       LEFT JOIN accounts a ON ct.account_id = a.id 
                       LEFT JOIN accounts admin ON ct.reviewed_by = admin.id 
                       $where_clause
                       ORDER BY ct.created_at DESC 
                       LIMIT $records_per_page OFFSET $offset";
    
    $transfers_result = $db->query($transfers_query, $params);
    $transfers = [];
    while ($row = $transfers_result->fetch_assoc()) {
        $transfers[] = $row;
    }
    
    // Get statistics
    $stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                    SUM(CASE WHEN status = 'declined' THEN 1 ELSE 0 END) as declined,
                    SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as total_approved_amount
                    FROM crypto_transfers";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    $error = "Failed to load transfers: " . $e->getMessage();
    $transfers = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'pending' => 0, 'approved' => 0, 'declined' => 0, 'total_approved_amount' => 0];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Crypto Transfers</li>
    </ol>
</nav>

<!-- Success/Error Messages -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-coins"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total']); ?></div>
                        <div class="text-muted">Total Transfers</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending']); ?></div>
                        <div class="text-muted">Pending Review</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['approved']); ?></div>
                        <div class="text-muted">Approved</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                            <i class="fas fa-times"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['declined']); ?></div>
                        <div class="text-muted">Declined</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">Filter Transfers</h3>
    </div>
    <div class="card-body">
        <form method="GET" action="" class="row g-3">
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                    <option value="declined" <?php echo $status_filter === 'declined' ? 'selected' : ''; ?>>Declined</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="crypto" class="form-label">Cryptocurrency</label>
                <select class="form-select" id="crypto" name="crypto">
                    <option value="">All Cryptocurrencies</option>
                    <option value="BTC" <?php echo $crypto_filter === 'BTC' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                    <option value="ETH" <?php echo $crypto_filter === 'ETH' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                    <option value="LTC" <?php echo $crypto_filter === 'LTC' ? 'selected' : ''; ?>>Litecoin (LTC)</option>
                    <option value="XRP" <?php echo $crypto_filter === 'XRP' ? 'selected' : ''; ?>>Ripple (XRP)</option>
                    <option value="ADA" <?php echo $crypto_filter === 'ADA' ? 'selected' : ''; ?>>Cardano (ADA)</option>
                    <option value="DOT" <?php echo $crypto_filter === 'DOT' ? 'selected' : ''; ?>>Polkadot (DOT)</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-2"></i>Filter
                </button>
                <a href="crypto-transfers.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Transfers Table -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-coins me-2"></i>
            Cryptocurrency Transfers
        </h3>
        <div class="card-subtitle">
            Manage and review cryptocurrency transfer requests
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($transfers)): ?>
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No transfers found</h5>
            <p class="text-muted">No cryptocurrency transfers match your current filters.</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-vcenter">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Cryptocurrency</th>
                        <th>Amount</th>
                        <th>Recipient</th>
                        <th>Transaction Hash</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transfers as $transfer): ?>
                    <tr>
                        <td>
                            <div class="d-flex py-1 align-items-center">
                                <div class="flex-fill">
                                    <div class="font-weight-medium"><?php echo htmlspecialchars($transfer['first_name'] . ' ' . $transfer['last_name']); ?></div>
                                    <div class="text-muted"><?php echo htmlspecialchars($transfer['username']); ?></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary"><?php echo htmlspecialchars($transfer['crypto_type']); ?></span>
                        </td>
                        <td class="text-muted">
                            <div class="font-weight-medium"><?php echo number_format($transfer['amount'], 8); ?></div>
                            <div class="text-muted small"><?php echo htmlspecialchars($transfer['crypto_type']); ?></div>
                        </td>
                        <td>
                            <code class="small"><?php echo substr($transfer['recipient_address'], 0, 15) . '...'; ?></code>
                        </td>
                        <td>
                            <code class="small"><?php echo substr($transfer['transaction_hash'], 0, 15) . '...'; ?></code>
                        </td>
                        <td class="text-muted">
                            <?php echo date('M j, Y', strtotime($transfer['created_at'])); ?>
                            <div class="small"><?php echo date('g:i A', strtotime($transfer['created_at'])); ?></div>
                        </td>
                        <td>
                            <span class="badge bg-<?php echo $transfer['status'] === 'approved' ? 'success' : ($transfer['status'] === 'declined' ? 'danger' : 'warning'); ?>">
                                <?php echo ucfirst($transfer['status']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-list flex-nowrap">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewTransfer(<?php echo $transfer['id']; ?>)">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if ($transfer['status'] === 'pending'): ?>
                                <button class="btn btn-sm btn-outline-success" onclick="reviewTransfer(<?php echo $transfer['id']; ?>, 'approve')">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="reviewTransfer(<?php echo $transfer['id']; ?>, 'decline')">
                                    <i class="fas fa-times"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Crypto transfers pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&crypto=<?php echo $crypto_filter; ?>">Previous</a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&crypto=<?php echo $crypto_filter; ?>"><?php echo $i; ?></a>
                </li>
                <?php endforeach; ?>
                
                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&crypto=<?php echo $crypto_filter; ?>">Next</a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Transfer Details Modal -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Transfer Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="transferModalBody">
                <!-- Transfer details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Review Transfer Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewModalTitle">Review Transfer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="transfer_id" id="reviewTransferId">
                    <input type="hidden" name="action" id="reviewAction">

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                  placeholder="Add notes about this decision (optional for approval, required for decline)"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="reviewActionText">This action will update the transfer status and notify the user via email.</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="reviewSubmitBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewTransfer(transferId) {
    // Load transfer details via AJAX (placeholder for now)
    document.getElementById('transferModalBody').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading transfer details...</p>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('transferModal'));
    modal.show();

    // Simulate loading delay
    setTimeout(() => {
        document.getElementById('transferModalBody').innerHTML = `
            <p>Transfer details for ID: ${transferId}</p>
            <p>This feature will be implemented in a future update.</p>
        `;
    }, 1000);
}

function reviewTransfer(transferId, action) {
    document.getElementById('reviewTransferId').value = transferId;
    document.getElementById('reviewAction').value = action;

    const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
    const title = document.getElementById('reviewModalTitle');
    const submitBtn = document.getElementById('reviewSubmitBtn');
    const actionText = document.getElementById('reviewActionText');
    const notesField = document.getElementById('admin_notes');

    if (action === 'approve') {
        title.textContent = 'Approve Transfer';
        submitBtn.textContent = 'Approve Transfer';
        submitBtn.className = 'btn btn-success';
        actionText.textContent = 'This will approve the transfer and notify the user that their cryptocurrency transfer has been processed.';
        notesField.placeholder = 'Add approval notes (optional)';
        notesField.required = false;
    } else {
        title.textContent = 'Decline Transfer';
        submitBtn.textContent = 'Decline Transfer';
        submitBtn.className = 'btn btn-danger';
        actionText.textContent = 'This will decline the transfer and notify the user. Please provide a reason for the decline.';
        notesField.placeholder = 'Reason for decline (required)';
        notesField.required = true;
    }

    modal.show();
}

// Auto-dismiss alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
});
</script>

<?php include 'includes/admin-footer.php'; ?>
