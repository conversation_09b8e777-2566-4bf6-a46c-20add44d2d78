<?php
/**
 * Internal Transfers Page
 * Allow users to transfer money between their own accounts
 */

// Set page variables
$page_title = 'Internal Transfers';
$current_page = 'accounts';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Get user's virtual cards and calculate total balance
$virtual_cards = [];
$total_virtual_card_balance = 0;
try {
    $cards_query = "SELECT * FROM virtual_cards WHERE account_id = ? ORDER BY created_at DESC";
    $cards_result = $db->query($cards_query, [$user_id]);
    while ($card = $cards_result->fetch_assoc()) {
        $virtual_cards[] = $card;
        // Sum up all card balances
        $card_balance = $card['card_balance'] ?? $card['balance'] ?? 0;
        $total_virtual_card_balance += $card_balance;
    }
} catch (Exception $e) {
    // Table might not exist, continue without error
    error_log("Virtual cards query error: " . $e->getMessage());
}

// Get recent internal transfers
$transfers_query = "SELECT * FROM transactions
                   WHERE user_id = ?
                   AND (description LIKE '%Internal Transfer%' OR description LIKE '%Account Transfer%')
                   ORDER BY created_at DESC
                   LIMIT 10";
$transfers_result = $db->query($transfers_query, [$user_id]);
$recent_transfers = [];
while ($transfer = $transfers_result->fetch_assoc()) {
    $recent_transfers[] = $transfer;
}

// Set page title
$page_title = 'Internal Transfers';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Accounts CSS -->
<link rel="stylesheet" href="accounts.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Accounts Hero Section -->
        <div class="accounts-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Internal Transfers</div>
                    <div class="hero-subtitle">Transfer money between your accounts and cards</div>
                    <div class="hero-stats">
                        Available Balance: <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-primary" onclick="viewTransferHistory()">
                        <i class="fas fa-history me-2"></i>Transfer History
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Available Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Available</div>
                    <div class="balance-amount available">
                        <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                    </div>
                    <div class="balance-subtitle">Main Account</div>
                </div>
            </div>

            <!-- Virtual Card Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Virtual Card</div>
                    <div class="balance-amount">
                        <?php echo formatCurrency($total_virtual_card_balance, $user['currency']); ?>
                    </div>
                    <div class="balance-subtitle">Card Balance</div>
                </div>
            </div>

            <!-- Total Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total</div>
                    <div class="balance-amount">
                        <?php echo formatCurrency(($user['balance'] + $total_virtual_card_balance), $user['currency']); ?>
                    </div>
                    <div class="balance-subtitle">Combined Balance</div>
                </div>
            </div>

            <!-- Account Status Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Account Status</div>
                    <div class="balance-amount" style="color: #10b981;">
                        <?php echo ucfirst($user['status']); ?>
                    </div>
                    <div class="balance-subtitle">Active Account</div>
                </div>
            </div>
        </div>

        <!-- Accounts Container -->
        <div class="accounts-container">

        <!-- Transfer Form -->
        <div class="transfer-section">
            <div class="transfer-card">
                <div class="card-header">
                    <h3><i class="fas fa-exchange-alt me-2"></i>Internal Transfer</h3>
                    <p>Transfer money between your accounts instantly</p>
                </div>
                
                <form id="internalTransferForm" class="transfer-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="fromAccount">From Account</label>
                            <select id="fromAccount" name="from_account" class="form-control" required>
                                <option value="">Select source account</option>
                                <option value="main_account">Main Account (<?php echo formatCurrency($user['balance'], $user['currency']); ?>)</option>
                                <?php if (count($virtual_cards) > 0): ?>
                                    <?php foreach ($virtual_cards as $card): ?>
                                        <option value="virtual_card_<?php echo $card['card_id']; ?>" data-balance="<?php echo $card['card_balance'] ?? $card['balance'] ?? 0; ?>">
                                            Virtual Card - <?php echo substr($card['card_number'], -4); ?>
                                            (<?php echo formatCurrency($card['card_balance'] ?? $card['balance'] ?? 0, $user['currency']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="virtual_card">Virtual Card (<?php echo formatCurrency($total_virtual_card_balance, $user['currency']); ?>)</option>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="transfer-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        
                        <div class="form-group">
                            <label for="toAccount">To Account</label>
                            <select id="toAccount" name="to_account" class="form-control" required>
                                <option value="">Select destination account</option>
                                <option value="main_account">Main Account (<?php echo formatCurrency($user['balance'], $user['currency']); ?>)</option>
                                <?php if (count($virtual_cards) > 0): ?>
                                   <?php foreach ($virtual_cards as $card): ?>
                                       <option value="virtual_card_<?php echo $card['card_id']; ?>" data-balance="<?php echo $card['card_balance'] ?? $card['balance'] ?? 0; ?>">
                                           Virtual Card - <?php echo substr($card['card_number'], -4); ?>
                                           (<?php echo formatCurrency($card['card_balance'] ?? $card['balance'] ?? 0, $user['currency']); ?>)
                                       </option>
                                   <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="virtual_card">Virtual Card (<?php echo formatCurrency($total_virtual_card_balance, $user['currency']); ?>)</option>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="transferAmount">Transfer Amount</label>
                        <div class="amount-input">
                            <span class="currency-symbol"><?php echo getCurrencySymbol($user['currency']); ?></span>
                            <input type="number" id="transferAmount" name="amount" class="form-control" 
                                   placeholder="0.00" step="0.01" min="0.01" required>
                        </div>
                        <div class="amount-suggestions">
                            <button type="button" class="amount-btn" onclick="setAmount(500)">$500</button>
                            <button type="button" class="amount-btn" onclick="setAmount(1000)">$1,000</button>
                            <button type="button" class="amount-btn" onclick="setAmount(2500)">$2,500</button>
                            <button type="button" class="amount-btn" onclick="setAmount(5000)">$5,000</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="transferNote">Transfer Note (Optional)</label>
                        <input type="text" id="transferNote" name="note" class="form-control" 
                               placeholder="Add a note for this transfer">
                    </div>
                    
                    <div class="transfer-summary" id="transferSummary" style="display: none;">
                        <h4>Transfer Summary</h4>
                        <div class="summary-row">
                            <span>From:</span>
                            <span id="summaryFrom">-</span>
                        </div>
                        <div class="summary-row">
                            <span>To:</span>
                            <span id="summaryTo">-</span>
                        </div>
                        <div class="summary-row">
                            <span>Amount:</span>
                            <span id="summaryAmount">-</span>
                        </div>
                        <div class="summary-row total">
                            <span>Transfer Fee:</span>
                            <span>FREE</span>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>Reset
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Transfer Now
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Recent Transfers -->
        <?php if (!empty($recent_transfers)): ?>
        <div class="recent-transfers">
            <div class="section-header">
                <h3><i class="fas fa-history me-2"></i>Recent Internal Transfers</h3>
                <a href="../transactions/" class="view-all-link">View All</a>
            </div>
            
            <div class="transfers-list">
                <?php foreach ($recent_transfers as $transfer): ?>
                <div class="transfer-item">
                    <div class="transfer-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="transfer-details">
                        <div class="transfer-description"><?php echo htmlspecialchars($transfer['description']); ?></div>
                        <div class="transfer-date"><?php echo date('M j, Y g:i A', strtotime($transfer['created_at'])); ?></div>
                    </div>
                    <div class="transfer-amount <?php echo $transfer['transaction_type'] === 'credit' ? 'credit' : 'debit'; ?>">
                        <?php echo $transfer['transaction_type'] === 'credit' ? '+' : '-'; ?><?php echo formatCurrency($transfer['amount'], $transfer['currency']); ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

</div>

<!-- Transfer Confirmation Modal -->
<div class="modal fade" id="transferConfirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content transfer-confirm-modal">
            <div class="modal-header transfer-confirm-header">
                <h5 class="modal-title">
                    <i class="fas fa-exchange-alt me-2"></i>Confirm Transfer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body transfer-confirm-body" id="confirmationContent">
                <!-- Confirmation content will be loaded here -->
            </div>
            <div class="modal-footer transfer-confirm-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="confirmTransferBtn">
                    <i class="fas fa-check me-2"></i>Confirm Transfer
                </button>
            </div>
        </div>
    </div>
</div>

        </div> <!-- End Content Container -->

    </div> <!-- End Main Content -->

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<script src="accounts.js"></script>

<script>
// Pass PHP data to JavaScript
window.userData = {
    mainBalance: <?php echo $user['balance']; ?>,
    virtualCards: <?php echo json_encode(array_map(function($card) {
        return [
            'id' => $card['card_id'],
            'balance' => $card['card_balance'] ?? $card['balance'] ?? 0
        ];
    }, $virtual_cards)); ?>,
    currency: '<?php echo $user['currency']; ?>'
};
</script>
