<?php
/**
 * IRS Tax Return Assistance Application Form
 * Allow users to apply for IRS tax return assistance
 */

// Set page variables
$page_title = 'IRS Tax Return Assistance';
$current_page = 'irs';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Check if IRS feature is enabled
$irs_enabled_query = "SELECT value FROM admin_settings WHERE setting_key = 'irs_feature_enabled'";
$irs_enabled_result = $db->query($irs_enabled_query);
$irs_enabled = $irs_enabled_result->fetch_assoc()['value'] ?? '0';

if ($irs_enabled !== '1') {
    header('Location: ../dashboard/index.php');
    exit();
}

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate required fields
        $required_fields = [
            'first_name', 'last_name', 'ssn', 'date_of_birth', 'phone_number', 'email',
            'street_address', 'city', 'state', 'zip_code', 'tax_year', 'filing_status',
            'annual_income', 'employment_type', 'bank_account_number', 'routing_number'
        ];
        
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Please fill in all required fields.");
            }
        }
        
        // Generate application number
        $application_number = 'IRS' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // Prepare dependents info as JSON
        $dependents_info = [];
        if (!empty($_POST['number_of_dependents']) && intval($_POST['number_of_dependents']) > 0) {
            $num_dependents = intval($_POST['number_of_dependents']);
            for ($i = 1; $i <= $num_dependents; $i++) {
                if (!empty($_POST["dependent_name_$i"])) {
                    $dependents_info[] = [
                        'name' => sanitizeInput($_POST["dependent_name_$i"]),
                        'relationship' => sanitizeInput($_POST["dependent_relationship_$i"] ?? ''),
                        'ssn' => sanitizeInput($_POST["dependent_ssn_$i"] ?? ''),
                        'date_of_birth' => sanitizeInput($_POST["dependent_dob_$i"] ?? '')
                    ];
                }
            }
        }
        
        // Insert application
        $insert_query = "INSERT INTO irs_applications (
            account_id, application_number, first_name, last_name, middle_name, ssn, date_of_birth,
            phone_number, email, street_address, city, state, zip_code, country, tax_year,
            filing_status, annual_income, employment_type, employer_name, employer_ein,
            number_of_dependents, dependents_info, bank_account_number, routing_number,
            previous_year_agi, estimated_refund, has_foreign_income, has_business_income,
            has_rental_income, has_investment_income, special_circumstances, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')";
        
        $application_id = $db->insert($insert_query, [
            $user_id,
            $application_number,
            sanitizeInput($_POST['first_name']),
            sanitizeInput($_POST['last_name']),
            sanitizeInput($_POST['middle_name'] ?? ''),
            sanitizeInput($_POST['ssn']),
            sanitizeInput($_POST['date_of_birth']),
            sanitizeInput($_POST['phone_number']),
            sanitizeInput($_POST['email']),
            sanitizeInput($_POST['street_address']),
            sanitizeInput($_POST['city']),
            sanitizeInput($_POST['state']),
            sanitizeInput($_POST['zip_code']),
            sanitizeInput($_POST['country'] ?? 'United States'),
            intval($_POST['tax_year']),
            sanitizeInput($_POST['filing_status']),
            floatval($_POST['annual_income']),
            sanitizeInput($_POST['employment_type']),
            sanitizeInput($_POST['employer_name'] ?? ''),
            sanitizeInput($_POST['employer_ein'] ?? ''),
            intval($_POST['number_of_dependents'] ?? 0),
            json_encode($dependents_info),
            sanitizeInput($_POST['bank_account_number']),
            sanitizeInput($_POST['routing_number']),
            floatval($_POST['previous_year_agi'] ?? 0),
            floatval($_POST['estimated_refund'] ?? 0),
            isset($_POST['has_foreign_income']) ? 1 : 0,
            isset($_POST['has_business_income']) ? 1 : 0,
            isset($_POST['has_rental_income']) ? 1 : 0,
            isset($_POST['has_investment_income']) ? 1 : 0,
            sanitizeInput($_POST['special_circumstances'] ?? '')
        ]);
        
        if ($application_id) {
            $success = "Your IRS tax return assistance application has been submitted successfully! Application Number: $application_number";
        } else {
            throw new Exception("Failed to submit application. Please try again.");
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Include header
require_once '../shared/header.php';
?>

<!-- Include IRS CSS -->
<link rel="stylesheet" href="irs.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="hero-content">
                <div class="hero-text">
                    <h1><i class="fas fa-file-invoice-dollar me-3"></i>IRS Tax Return Assistance</h1>
                    <p class="hero-subtitle">Professional tax return preparation and filing assistance</p>
                </div>
                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Support</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">Fast</div>
                        <div class="stat-label">Processing</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($success)): ?>
        <div class="alert alert-success" role="alert">
            <div class="d-flex">
                <div><i class="fas fa-check-circle me-2"></i></div>
                <div>
                    <h4 class="alert-title">Application Submitted!</h4>
                    <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
        <div class="alert alert-danger" role="alert">
            <div class="d-flex">
                <div><i class="fas fa-exclamation-circle me-2"></i></div>
                <div>
                    <h4 class="alert-title">Error!</h4>
                    <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Application Form -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-file-alt me-2"></i>
                            Tax Return Assistance Application
                        </h3>
                        <div class="card-subtitle">
                            Please fill out all required information accurately
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" id="irsApplicationForm">
                            <!-- Personal Information Section -->
                            <div class="form-section">
                                <div class="section-title">
                                    <i class="fas fa-user me-2"></i>Personal Information
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="first_name" class="form-label">First Name *</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                                   value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="middle_name" class="form-label">Middle Name</label>
                                            <input type="text" class="form-control" id="middle_name" name="middle_name" 
                                                   value="<?php echo htmlspecialchars($user['middle_name'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="last_name" class="form-label">Last Name *</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                                   value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ssn" class="form-label">Social Security Number *</label>
                                            <input type="text" class="form-control" id="ssn" name="ssn" 
                                                   placeholder="XXX-XX-XXXX" pattern="[0-9]{3}-[0-9]{2}-[0-9]{4}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="date_of_birth" class="form-label">Date of Birth *</label>
                                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone_number" class="form-label">Phone Number *</label>
                                            <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email Address *</label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information Section -->
                            <div class="form-section">
                                <div class="section-title">
                                    <i class="fas fa-map-marker-alt me-2"></i>Address Information
                                </div>
                                
                                <div class="mb-3">
                                    <label for="street_address" class="form-label">Street Address *</label>
                                    <input type="text" class="form-control" id="street_address" name="street_address" 
                                           value="<?php echo htmlspecialchars($user['address'] ?? ''); ?>" required>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="city" class="form-label">City *</label>
                                            <input type="text" class="form-control" id="city" name="city" 
                                                   value="<?php echo htmlspecialchars($user['city'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="state" class="form-label">State *</label>
                                            <select class="form-select" id="state" name="state" required>
                                                <option value="">Select State</option>
                                                <option value="AL">Alabama</option>
                                                <option value="AK">Alaska</option>
                                                <option value="AZ">Arizona</option>
                                                <option value="AR">Arkansas</option>
                                                <option value="CA">California</option>
                                                <option value="CO">Colorado</option>
                                                <option value="CT">Connecticut</option>
                                                <option value="DE">Delaware</option>
                                                <option value="FL">Florida</option>
                                                <option value="GA">Georgia</option>
                                                <!-- Add more states as needed -->
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="zip_code" class="form-label">ZIP Code *</label>
                                            <input type="text" class="form-control" id="zip_code" name="zip_code" 
                                                   pattern="[0-9]{5}(-[0-9]{4})?" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tax Information Section -->
                            <div class="form-section">
                                <div class="section-title">
                                    <i class="fas fa-calculator me-2"></i>Tax Information
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="tax_year" class="form-label">Tax Year *</label>
                                            <select class="form-select" id="tax_year" name="tax_year" required>
                                                <option value="">Select Tax Year</option>
                                                <option value="2023">2023</option>
                                                <option value="2022">2022</option>
                                                <option value="2021">2021</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="filing_status" class="form-label">Filing Status *</label>
                                            <select class="form-select" id="filing_status" name="filing_status" required>
                                                <option value="">Select Filing Status</option>
                                                <option value="single">Single</option>
                                                <option value="married_filing_jointly">Married Filing Jointly</option>
                                                <option value="married_filing_separately">Married Filing Separately</option>
                                                <option value="head_of_household">Head of Household</option>
                                                <option value="qualifying_widow">Qualifying Widow(er)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="annual_income" class="form-label">Annual Income *</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="annual_income" name="annual_income"
                                                       min="0" step="0.01" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="employment_type" class="form-label">Employment Type *</label>
                                            <select class="form-select" id="employment_type" name="employment_type" required>
                                                <option value="">Select Employment Type</option>
                                                <option value="employed">Employed</option>
                                                <option value="self_employed">Self-Employed</option>
                                                <option value="unemployed">Unemployed</option>
                                                <option value="retired">Retired</option>
                                                <option value="student">Student</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row" id="employerFields" style="display: none;">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="employer_name" class="form-label">Employer Name</label>
                                            <input type="text" class="form-control" id="employer_name" name="employer_name">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="employer_ein" class="form-label">Employer EIN</label>
                                            <input type="text" class="form-control" id="employer_ein" name="employer_ein"
                                                   placeholder="XX-XXXXXXX" pattern="[0-9]{2}-[0-9]{7}">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Dependents Section -->
                            <div class="form-section">
                                <div class="section-title">
                                    <i class="fas fa-users me-2"></i>Dependents Information
                                </div>

                                <div class="mb-3">
                                    <label for="number_of_dependents" class="form-label">Number of Dependents</label>
                                    <select class="form-select" id="number_of_dependents" name="number_of_dependents">
                                        <option value="0">0</option>
                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                        <option value="4">4</option>
                                        <option value="5">5+</option>
                                    </select>
                                </div>

                                <div id="dependentsContainer"></div>
                            </div>

                            <!-- Financial Information Section -->
                            <div class="form-section">
                                <div class="section-title">
                                    <i class="fas fa-university me-2"></i>Banking Information
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="bank_account_number" class="form-label">Bank Account Number *</label>
                                            <input type="text" class="form-control" id="bank_account_number" name="bank_account_number" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="routing_number" class="form-label">Routing Number *</label>
                                            <input type="text" class="form-control" id="routing_number" name="routing_number"
                                                   pattern="[0-9]{9}" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="previous_year_agi" class="form-label">Previous Year AGI</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="previous_year_agi" name="previous_year_agi"
                                                       min="0" step="0.01">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="estimated_refund" class="form-label">Estimated Refund</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="estimated_refund" name="estimated_refund"
                                                       min="0" step="0.01">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Information Section -->
                            <div class="form-section">
                                <div class="section-title">
                                    <i class="fas fa-info-circle me-2"></i>Additional Information
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_foreign_income" name="has_foreign_income">
                                            <label class="form-check-label" for="has_foreign_income">
                                                I have foreign income
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_business_income" name="has_business_income">
                                            <label class="form-check-label" for="has_business_income">
                                                I have business income
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_rental_income" name="has_rental_income">
                                            <label class="form-check-label" for="has_rental_income">
                                                I have rental income
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_investment_income" name="has_investment_income">
                                            <label class="form-check-label" for="has_investment_income">
                                                I have investment income
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="special_circumstances" class="form-label">Special Circumstances</label>
                                    <textarea class="form-control" id="special_circumstances" name="special_circumstances"
                                              rows="4" placeholder="Please describe any special circumstances or additional information..."></textarea>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Submit Application
                                </button>
                                <a href="../dashboard/index.php" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once __DIR__ . '/../shared/user_footer.php'; ?>
</div>

<style>
.section-title {
    font-size: 1.1rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.section-title i {
    color: var(--primary-color, #007bff);
}
</style>

<script src="irs.js"></script>
