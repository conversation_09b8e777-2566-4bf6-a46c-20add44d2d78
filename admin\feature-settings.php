<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Feature Settings';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        // Feature toggles
        $irs_enabled = isset($_POST['irs_feature_enabled']) ? '1' : '0';
        $crypto_transfers_enabled = isset($_POST['crypto_transfers_enabled']) ? '1' : '0';
        $crypto_deposits_enabled = isset($_POST['crypto_deposits_enabled']) ? '1' : '0';
        $card_topups_enabled = isset($_POST['card_topups_enabled']) ? '1' : '0';
        
        // Update feature settings
        $features = [
            'irs_feature_enabled' => $irs_enabled,
            'crypto_transfers_enabled' => $crypto_transfers_enabled,
            'crypto_deposits_enabled' => $crypto_deposits_enabled,
            'card_topups_enabled' => $card_topups_enabled
        ];
        
        $updated_count = 0;
        foreach ($features as $key => $value) {
            $update_query = "INSERT INTO admin_settings (setting_key, setting_value, setting_type, description) 
                            VALUES (?, ?, 'boolean', ?) 
                            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
            
            $descriptions = [
                'irs_feature_enabled' => 'Enable/disable IRS tax return assistance feature',
                'crypto_transfers_enabled' => 'Enable/disable crypto transfer feature',
                'crypto_deposits_enabled' => 'Enable/disable crypto deposit feature',
                'card_topups_enabled' => 'Enable/disable virtual card top-up feature'
            ];
            
            $db->query($update_query, [$key, $value, $descriptions[$key]]);
            $updated_count++;
        }
        
        $success = "Successfully updated $updated_count feature settings.";
        
    } catch (Exception $e) {
        $error = "Error updating feature settings: " . $e->getMessage();
    }
}

// Get current feature settings
$current_settings = [
    'irs_feature_enabled' => '1',
    'crypto_transfers_enabled' => '1',
    'crypto_deposits_enabled' => '1',
    'card_topups_enabled' => '1'
];

try {
    $db = getDB();

    // First check if admin_settings table exists
    $table_check = $db->query("SHOW TABLES LIKE 'admin_settings'");
    if ($table_check->num_rows == 0) {
        // Table doesn't exist, create it
        $create_table = "CREATE TABLE `admin_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
            `setting_value` text COLLATE utf8mb4_unicode_ci,
            `setting_type` enum('boolean','string','integer','json') COLLATE utf8mb4_unicode_ci DEFAULT 'string',
            `description` text COLLATE utf8mb4_unicode_ci,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $db->query($create_table);

        // Insert default settings
        $default_settings = [
            ['irs_feature_enabled', '1', 'boolean', 'Enable/disable IRS tax return assistance feature'],
            ['crypto_transfers_enabled', '1', 'boolean', 'Enable/disable crypto transfer feature'],
            ['crypto_deposits_enabled', '1', 'boolean', 'Enable/disable crypto deposit feature'],
            ['card_topups_enabled', '1', 'boolean', 'Enable/disable virtual card top-up feature']
        ];

        foreach ($default_settings as $setting) {
            $insert_query = "INSERT INTO admin_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)";
            $db->query($insert_query, $setting);
        }
    }

    // Now get the settings
    $settings_query = "SELECT setting_key, value FROM admin_settings WHERE setting_key IN ('irs_feature_enabled', 'crypto_transfers_enabled', 'crypto_deposits_enabled', 'card_topups_enabled')";
    $settings_result = $db->query($settings_query);

    if ($settings_result) {
        while ($row = $settings_result->fetch_assoc()) {
            $current_settings[$row['setting_key']] = $row['value'];
        }
    }

} catch (Exception $e) {
    if (!isset($error)) {
        $error = "Failed to load feature settings: " . $e->getMessage();
    }
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Feature Settings</li>
    </ol>
</nav>

<!-- Success/Error Messages -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Feature Settings Form -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-toggle-on me-2"></i>
                    Feature Management
                </h3>
                <div class="card-subtitle">
                    Enable or disable banking features for all users
                </div>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <!-- IRS Tax Return Assistance -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <span class="avatar bg-primary">
                                                <i class="fas fa-file-invoice-dollar"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill">
                                            <h4 class="card-title mb-1">IRS Tax Return Assistance</h4>
                                            <p class="text-muted mb-2">Allow users to apply for professional tax return preparation and filing assistance</p>
                                            <label class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="irs_feature_enabled" 
                                                       <?php echo $current_settings['irs_feature_enabled'] === '1' ? 'checked' : ''; ?>>
                                                <span class="form-check-label">
                                                    <?php echo $current_settings['irs_feature_enabled'] === '1' ? 'Enabled' : 'Disabled'; ?>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Crypto Transfers -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <span class="avatar bg-warning">
                                                <i class="fas fa-coins"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill">
                                            <h4 class="card-title mb-1">Crypto Transfers</h4>
                                            <p class="text-muted mb-2">Enable simulated cryptocurrency transfer functionality for users</p>
                                            <label class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="crypto_transfers_enabled" 
                                                       <?php echo $current_settings['crypto_transfers_enabled'] === '1' ? 'checked' : ''; ?>>
                                                <span class="form-check-label">
                                                    <?php echo $current_settings['crypto_transfers_enabled'] === '1' ? 'Enabled' : 'Disabled'; ?>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Crypto Deposits -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <span class="avatar bg-success">
                                                <i class="fas fa-wallet"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill">
                                            <h4 class="card-title mb-1">Crypto Deposits</h4>
                                            <p class="text-muted mb-2">Allow users to deposit cryptocurrency with admin approval workflow</p>
                                            <label class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="crypto_deposits_enabled" 
                                                       <?php echo $current_settings['crypto_deposits_enabled'] === '1' ? 'checked' : ''; ?>>
                                                <span class="form-check-label">
                                                    <?php echo $current_settings['crypto_deposits_enabled'] === '1' ? 'Enabled' : 'Disabled'; ?>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Virtual Card Top-ups -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <span class="avatar bg-info">
                                                <i class="fas fa-credit-card"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill">
                                            <h4 class="card-title mb-1">Virtual Card Top-ups</h4>
                                            <p class="text-muted mb-2">Enable virtual card top-up functionality with multiple payment methods</p>
                                            <label class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="card_topups_enabled" 
                                                       <?php echo $current_settings['card_topups_enabled'] === '1' ? 'checked' : ''; ?>>
                                                <span class="form-check-label">
                                                    <?php echo $current_settings['card_topups_enabled'] === '1' ? 'Enabled' : 'Disabled'; ?>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-actions text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>
                            Save Feature Settings
                        </button>
                        <a href="index.php" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Feature Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Feature Information
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <div class="d-flex">
                        <div><i class="fas fa-lightbulb me-2"></i></div>
                        <div>
                            <h4 class="alert-title">Important Notes</h4>
                            <ul class="mb-0">
                                <li><strong>Real-time Effect:</strong> Changes take effect immediately for all users</li>
                                <li><strong>Navigation:</strong> Disabled features will be hidden from user navigation menus</li>
                                <li><strong>Access Control:</strong> Users cannot access disabled features even with direct URLs</li>
                                <li><strong>Admin Access:</strong> Admin panels for these features remain accessible regardless of user settings</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add dynamic label updates
document.addEventListener('DOMContentLoaded', function() {
    const switches = document.querySelectorAll('.form-check-input[type="checkbox"]');
    
    switches.forEach(function(switchEl) {
        switchEl.addEventListener('change', function() {
            const label = this.nextElementSibling;
            label.textContent = this.checked ? 'Enabled' : 'Disabled';
        });
    });
});
</script>

<?php include 'includes/admin-footer.php'; ?>
