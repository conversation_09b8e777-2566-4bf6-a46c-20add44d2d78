/* FontAwesome Essential Icons Fallback */
/* This provides basic icons when FontAwesome CDN fails */

.fas, .far, .fab {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", sans-serif;
    font-weight: 900;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    display: inline-block;
}

.far {
    font-weight: 400;
}

/* Essential Icons using flat symbols */
.fa-user::before, .fas.fa-user::before { content: "U"; }
.fa-home::before, .fas.fa-home::before { content: "H"; }
.fa-dashboard::before, .fas.fa-dashboard::before { content: "D"; }
.fa-tachometer-alt::before, .fas.fa-tachometer-alt::before { content: "D"; }
.fa-exchange-alt::before, .fas.fa-exchange-alt::before { content: "⇄"; }
.fa-credit-card::before, .fas.fa-credit-card::before { content: "C"; }
.fa-university::before, .fas.fa-university::before { content: "B"; }
.fa-money-bill::before, .fas.fa-money-bill::before { content: "$"; }
.fa-wallet::before, .fas.fa-wallet::before { content: "W"; }
.fa-chart-line::before, .fas.fa-chart-line::before { content: "↗"; }
.fa-cog::before, .fas.fa-cog::before { content: "⚙"; }
.fa-settings::before, .fas.fa-settings::before { content: "⚙"; }
.fa-sign-out-alt::before, .fas.fa-sign-out-alt::before { content: "→"; }
.fa-bell::before, .fas.fa-bell::before { content: "!"; }
.fa-envelope::before, .fas.fa-envelope::before { content: "@"; }
.fa-phone::before, .fas.fa-phone::before { content: "☎"; }
.fa-address-book::before, .fas.fa-address-book::before { content: "≡"; }
.fa-shield-alt::before, .fas.fa-shield-alt::before { content: "◊"; }
.fa-lock::before, .fas.fa-lock::before { content: "🔒"; }
.fa-key::before, .fas.fa-key::before { content: "K"; }
.fa-eye::before, .fas.fa-eye::before { content: "👁"; }
.fa-eye-slash::before, .fas.fa-eye-slash::before { content: "⊘"; }
.fa-check::before, .fas.fa-check::before { content: "✓"; }
.fa-times::before, .fas.fa-times::before { content: "✕"; }
.fa-plus::before, .fas.fa-plus::before { content: "+"; }
.fa-minus::before, .fas.fa-minus::before { content: "-"; }
.fa-edit::before, .fas.fa-edit::before { content: "✏️"; }
.fa-trash::before, .fas.fa-trash::before { content: "🗑️"; }
.fa-download::before, .fas.fa-download::before { content: "⬇️"; }
.fa-upload::before, .fas.fa-upload::before { content: "⬆️"; }
.fa-print::before, .fas.fa-print::before { content: "🖨️"; }
.fa-search::before, .fas.fa-search::before { content: "🔍"; }
.fa-filter::before, .fas.fa-filter::before { content: "🔽"; }
.fa-sort::before, .fas.fa-sort::before { content: "↕️"; }
.fa-calendar::before, .fas.fa-calendar::before { content: "📅"; }
.fa-clock::before, .fas.fa-clock::before { content: "🕐"; }
.fa-history::before, .fas.fa-history::before { content: "🕐"; }
.fa-info-circle::before, .fas.fa-info-circle::before { content: "ℹ️"; }
.fa-exclamation-triangle::before, .fas.fa-exclamation-triangle::before { content: "⚠️"; }
.fa-check-circle::before, .fas.fa-check-circle::before { content: "✅"; }
.fa-times-circle::before, .fas.fa-times-circle::before { content: "❌"; }
.fa-question-circle::before, .fas.fa-question-circle::before { content: "❓"; }
.fa-arrow-left::before, .fas.fa-arrow-left::before { content: "←"; }
.fa-arrow-right::before, .fas.fa-arrow-right::before { content: "→"; }
.fa-arrow-up::before, .fas.fa-arrow-up::before { content: "↑"; }
.fa-arrow-down::before, .fas.fa-arrow-down::before { content: "↓"; }
.fa-chevron-left::before, .fas.fa-chevron-left::before { content: "‹"; }
.fa-chevron-right::before, .fas.fa-chevron-right::before { content: "›"; }
.fa-chevron-up::before, .fas.fa-chevron-up::before { content: "^"; }
.fa-chevron-down::before, .fas.fa-chevron-down::before { content: "v"; }
.fa-bars::before, .fas.fa-bars::before { content: "☰"; }
.fa-ellipsis-v::before, .fas.fa-ellipsis-v::before { content: "⋮"; }
.fa-ellipsis-h::before, .fas.fa-ellipsis-h::before { content: "⋯"; }
.fa-copy::before, .fas.fa-copy::before { content: "📋"; }
.fa-paste::before, .fas.fa-paste::before { content: "📄"; }
.fa-cut::before, .fas.fa-cut::before { content: "✂️"; }
.fa-save::before, .fas.fa-save::before { content: "💾"; }
.fa-folder::before, .fas.fa-folder::before { content: "📁"; }
.fa-file::before, .fas.fa-file::before { content: "📄"; }
.fa-image::before, .fas.fa-image::before { content: "🖼️"; }
.fa-video::before, .fas.fa-video::before { content: "🎥"; }
.fa-music::before, .fas.fa-music::before { content: "🎵"; }
.fa-headset::before, .fas.fa-headset::before { content: "🎧"; }
.fa-microphone::before, .fas.fa-microphone::before { content: "🎤"; }
.fa-camera::before, .fas.fa-camera::before { content: "📷"; }
.fa-globe::before, .fas.fa-globe::before { content: "🌐"; }
.fa-wifi::before, .fas.fa-wifi::before { content: "📶"; }
.fa-bluetooth::before, .fas.fa-bluetooth::before { content: "📶"; }
.fa-battery-full::before, .fas.fa-battery-full::before { content: "🔋"; }
.fa-power-off::before, .fas.fa-power-off::before { content: "⏻"; }
.fa-refresh::before, .fas.fa-refresh::before { content: "🔄"; }
.fa-sync::before, .fas.fa-sync::before { content: "🔄"; }
.fa-undo::before, .fas.fa-undo::before { content: "↶"; }
.fa-redo::before, .fas.fa-redo::before { content: "↷"; }
.fa-star::before, .fas.fa-star::before { content: "⭐"; }
.fa-heart::before, .fas.fa-heart::before { content: "❤️"; }
.fa-thumbs-up::before, .fas.fa-thumbs-up::before { content: "👍"; }
.fa-thumbs-down::before, .fas.fa-thumbs-down::before { content: "👎"; }
.fa-share::before, .fas.fa-share::before { content: "📤"; }
.fa-link::before, .fas.fa-link::before { content: "🔗"; }
.fa-paperclip::before, .fas.fa-paperclip::before { content: "📎"; }
.fa-tag::before, .fas.fa-tag::before { content: "🏷️"; }
.fa-bookmark::before, .fas.fa-bookmark::before { content: "🔖"; }
.fa-flag::before, .fas.fa-flag::before { content: "🚩"; }
.fa-map::before, .fas.fa-map::before { content: "🗺️"; }
.fa-location::before, .fas.fa-location::before { content: "📍"; }
.fa-compass::before, .fas.fa-compass::before { content: "🧭"; }
.fa-car::before, .fas.fa-car::before { content: "🚗"; }
.fa-plane::before, .fas.fa-plane::before { content: "✈️"; }
.fa-train::before, .fas.fa-train::before { content: "🚆"; }
.fa-ship::before, .fas.fa-ship::before { content: "🚢"; }
.fa-bicycle::before, .fas.fa-bicycle::before { content: "🚲"; }
.fa-walking::before, .fas.fa-walking::before { content: "🚶"; }
.fa-running::before, .fas.fa-running::before { content: "🏃"; }

/* Banking specific icons */
.fa-dollar-sign::before, .fas.fa-dollar-sign::before { content: "$"; }
.fa-euro-sign::before, .fas.fa-euro-sign::before { content: "€"; }
.fa-pound-sign::before, .fas.fa-pound-sign::before { content: "£"; }
.fa-yen-sign::before, .fas.fa-yen-sign::before { content: "¥"; }
.fa-coins::before, .fas.fa-coins::before { content: "○"; }
.fa-piggy-bank::before, .fas.fa-piggy-bank::before { content: "S"; }
.fa-receipt::before, .fas.fa-receipt::before { content: "R"; }
.fa-calculator::before, .fas.fa-calculator::before { content: "="; }
.fa-chart-bar::before, .fas.fa-chart-bar::before { content: "▬"; }
.fa-chart-pie::before, .fas.fa-chart-pie::before { content: "◐"; }
.fa-percentage::before, .fas.fa-percentage::before { content: "%"; }

/* Comprehensive flat icon styling for all dashboard icons */
.fas, .far, .fab {
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
    text-shadow: none !important;
    filter: none !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Flat white icon styling for fallback icons */
.stat-icon .fas, .stat-icon .far, .stat-icon .fab,
.balance-icon .fas, .balance-icon .far, .balance-icon .fab {
    color: white !important;
    background: none !important;
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: none !important;
    filter: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Ensure fallback icons are flat and white in stat cards */
.stat-card .stat-icon .fas::before,
.stat-card .stat-icon .far::before,
.stat-card .stat-icon .fab::before,
.balance-card-new .balance-icon .fas::before,
.balance-card-new .balance-icon .far::before,
.balance-card-new .balance-icon .fab::before {
    color: white !important;
    text-shadow: none !important;
    filter: none !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Quick links icons should be flat and inherit color */
.quick-link .fas, .quick-link .far, .quick-link .fab {
    color: inherit !important;
    background: none !important;
    text-shadow: none !important;
    filter: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Navigation icons should be flat */
.nav-link .fas, .nav-link .far, .nav-link .fab,
.sidebar-nav .fas, .sidebar-nav .far, .sidebar-nav .fab {
    color: inherit !important;
    background: none !important;
    text-shadow: none !important;
    filter: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Utility classes */
.fa-fw {
    text-align: center;
    width: 1.25em;
}

.fa-lg {
    font-size: 1.33333em;
    line-height: 0.75em;
    vertical-align: -0.0667em;
}

.fa-2x { font-size: 2em; }
.fa-3x { font-size: 3em; }

.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Margin utilities for icons */
.me-1 { margin-right: 0.25rem; }
.me-2 { margin-right: 0.5rem; }
.ms-1 { margin-left: 0.25rem; }
.ms-2 { margin-left: 0.5rem; }
