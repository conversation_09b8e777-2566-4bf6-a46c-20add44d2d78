/**
 * Cheque Preview Page JavaScript
 * Handles cheque book management and preview functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Cheque preview page loaded');
});

/**
 * Request a new cheque book
 */
function requestChequeBook() {
    // Show confirmation dialog
    if (confirm('Would you like to request a new cheque book? This may take 3-5 business days to process.')) {
        // Here you would typically make an API call to request a cheque book
        showNotification('Cheque book request submitted successfully! You will be notified when it\'s ready.', 'success');
        
        // Simulate API call
        setTimeout(() => {
            console.log('Cheque book request processed');
        }, 1000);
    }
}

/**
 * Print the current cheque
 */
function printCheque() {
    // Hide non-printable elements and trigger print
    const printContent = document.querySelector('.cheque-preview');
    if (printContent) {
        window.print();
        showNotification('Print dialog opened', 'info');
    } else {
        showNotification('No cheque available to print', 'error');
    }
}

/**
 * Download cheque as PDF
 */
function downloadCheque() {
    // This would typically generate a PDF of the cheque
    showNotification('PDF download feature coming soon!', 'info');
    
    // Simulate PDF generation
    setTimeout(() => {
        console.log('PDF would be generated here');
    }, 1000);
}

/**
 * Show notification to user
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `;
    
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Format currency for display
 */
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

/**
 * Validate cheque information
 */
function validateChequeInfo() {
    // This would validate cheque details before printing/downloading
    return true;
}

/**
 * Handle cheque book status updates
 */
function updateChequeBookStatus(status) {
    const statusElement = document.querySelector('.balance-amount');
    if (statusElement) {
        statusElement.textContent = status;
    }
}

/**
 * Load cheque history (if needed)
 */
function loadChequeHistory() {
    // This would load the user's cheque usage history
    console.log('Loading cheque history...');
}

/**
 * Refresh cheque status
 */
function refreshChequeStatus() {
    showNotification('Refreshing cheque status...', 'info');
    setTimeout(() => {
        location.reload();
    }, 1000);
}

/**
 * Preview a specific cheque
 */
function previewCheque(chequeId) {
    // Show modal with cheque preview
    const modal = new bootstrap.Modal(document.getElementById('chequePreviewModal'));
    const content = document.getElementById('chequePreviewContent');

    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading cheque preview...</p>
        </div>
    `;

    modal.show();

    // Fetch cheque data from server
    fetch(`get-cheque-details.php?id=${chequeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const cheque = data.cheque;
                content.innerHTML = `
                    <div class="cheque-preview-container">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cheque-details">
                                    <h6 class="mb-3"><i class="fas fa-info-circle me-2"></i>Cheque Details</h6>
                                    <div class="detail-row">
                                        <span class="detail-label">Cheque Number:</span>
                                        <span class="detail-value">${cheque.cheque_number}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Amount:</span>
                                        <span class="detail-value">${cheque.currency} ${parseFloat(cheque.amount).toLocaleString()}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Sender:</span>
                                        <span class="detail-value">${cheque.sender_name}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Bank:</span>
                                        <span class="detail-value">${cheque.bank_name}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Deposit Date:</span>
                                        <span class="detail-value">${new Date(cheque.deposit_date).toLocaleDateString()}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Status:</span>
                                        <span class="detail-value">
                                            <span class="badge bg-${cheque.clearance_status === 'cleared' ? 'success' : cheque.clearance_status === 'pending' ? 'warning' : 'danger'}">
                                                ${cheque.clearance_status.charAt(0).toUpperCase() + cheque.clearance_status.slice(1)}
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                ${cheque.cheque_image_path ? `
                                    <div class="cheque-image-preview">
                                        <h6 class="mb-3"><i class="fas fa-image me-2"></i>Cheque Image</h6>
                                        <img src="../../${cheque.cheque_image_path}"
                                             alt="Cheque Image"
                                             class="img-fluid rounded border"
                                             style="max-height: 300px; width: 100%; object-fit: contain; cursor: pointer;"
                                             onclick="viewChequeImage('${cheque.cheque_image_path}')">
                                        <p class="text-muted mt-2 small">Click image to view full size</p>
                                    </div>
                                ` : `
                                    <div class="no-image-preview">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-image fa-3x mb-3"></i>
                                            <p>No image available for this cheque</p>
                                        </div>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                    <style>
                        .detail-row {
                            display: flex;
                            justify-content: space-between;
                            padding: 0.5rem 0;
                            border-bottom: 1px solid #f0f0f0;
                        }
                        .detail-label {
                            font-weight: 600;
                            color: #666;
                        }
                        .detail-value {
                            color: #333;
                        }
                        .cheque-preview-container {
                            padding: 1rem;
                        }
                    </style>
                `;
            } else {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading cheque details: ${data.message || 'Unknown error'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error fetching cheque details:', error);
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading cheque preview. Please try again.
                </div>
            `;
        });
}

/**
 * View cheque image
 */
function viewChequeImage(imagePath) {
    // Open image in new window or modal
    const imageUrl = '../../' + imagePath;
    window.open(imageUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
}

/**
 * Download receipt for cleared cheque
 */
function downloadReceipt(chequeId) {
    showNotification('Generating receipt for cheque #' + chequeId + '...', 'info');
    // This would generate and download a receipt PDF
}

/**
 * Export functions for global use
 */
window.ChequePreview = {
    requestChequeBook,
    printCheque,
    downloadCheque,
    showNotification,
    formatCurrency,
    validateChequeInfo,
    updateChequeBookStatus,
    loadChequeHistory,
    refreshChequeStatus,
    previewCheque,
    viewChequeImage,
    downloadReceipt
};
