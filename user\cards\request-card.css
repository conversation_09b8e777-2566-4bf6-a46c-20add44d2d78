/* Card Request Page Styles */

.card-request-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.request-form-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e5e7eb;
    margin-bottom: 2rem;
}

.request-form-card .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

.request-form-card .card-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.request-form-card .card-body {
    padding: 2rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 2.5rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section h4 {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

/* Card Type Selection */
.card-type-selection {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.card-type-option {
    position: relative;
}

.card-type-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.card-type-label {
    display: block;
    background: #f8f9fa;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.card-type-label:hover {
    border-color: var(--primary-color);
    background: #f0f7ff;
}

.card-type-option input[type="radio"]:checked + .card-type-label {
    border-color: var(--primary-color);
    background: #f0f7ff;
    box-shadow: 0 0 0 3px rgba(32, 107, 196, 0.1);
}

.card-type-visual {
    margin-bottom: 1rem;
}

.card-type-visual svg {
    margin-bottom: 0.5rem;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.card-type-visual i {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.card-type-visual.visa i {
    color: #1a1f71;
}

.card-type-visual.mastercard i {
    color: #eb001b;
}

.card-type-visual span {
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
}

.card-type-info p {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.card-type-info small {
    color: #6b7280;
    line-height: 1.4;
}

/* Limit Selection */
.limit-selection .form-group {
    margin-bottom: 1rem;
}

.limit-selection .form-control {
    height: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0 1rem;
}

.limit-selection .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(32, 107, 196, 0.1);
}

.limit-info {
    margin-top: 0.5rem;
}

.limit-suggestions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.limit-btn {
    background: #f8f9fa;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    transition: all 0.3s ease;
}

.limit-btn:hover {
    border-color: var(--primary-color);
    background: #f0f7ff;
    color: var(--primary-color);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.form-actions .btn {
    min-width: 150px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
}

/* Status Card */
.status-card, .pending-apps-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.status-card .card-header, .pending-apps-card .card-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #e5e7eb;
}

.status-card .card-header h4, .pending-apps-card .card-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
}

.status-card .card-body, .pending-apps-card .card-body {
    padding: 1.5rem;
}

.status-item {
    margin-bottom: 1rem;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-item label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
}

/* Pending Applications */
.pending-app-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e5e7eb;
}

.pending-app-item:last-child {
    margin-bottom: 0;
}

.app-id {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.app-details {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.app-type {
    font-weight: 600;
    color: #374151;
}

.app-limit {
    color: #6b7280;
    font-size: 0.875rem;
}

.app-date {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-type-selection {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        width: 100%;
    }
    
    .limit-suggestions {
        justify-content: center;
    }
    
    .request-form-card .card-body {
        padding: 1.5rem;
    }
}
