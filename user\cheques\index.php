<?php
/**
 * Cheques Page
 * View deposited cheques and their status
 */

// Set page variables
$page_title = 'Cheque Deposits';
$current_page = 'cheques';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../auth/login.php');
    exit();
}

// Get user's deposited cheques from the database
$cheques = [];
try {
    $cheques_query = "SELECT * FROM cheque_deposits WHERE account_id = ? ORDER BY created_at DESC";
    $cheques_result = $db->query($cheques_query, [$user_id]);
    while ($cheque = $cheques_result->fetch_assoc()) {
        $cheques[] = $cheque;
    }
} catch (Exception $e) {
    // Table might not exist, continue without error
    error_log("Cheque deposits query error: " . $e->getMessage());
}

// Calculate stats
$total_cheques = count($cheques);
$pending_cheques = count(array_filter($cheques, function($c) { return $c['clearance_status'] === 'pending'; }));
$cleared_cheques = count(array_filter($cheques, function($c) { return $c['clearance_status'] === 'cleared'; }));
$processing_cheques = count(array_filter($cheques, function($c) { return $c['clearance_status'] === 'processing'; }));
$rejected_cheques = count(array_filter($cheques, function($c) { return $c['clearance_status'] === 'rejected'; }));

// Calculate total amount of cleared cheques
$total_cleared_amount = 0;
foreach ($cheques as $cheque) {
    if ($cheque['clearance_status'] === 'cleared') {
        $total_cleared_amount += $cheque['amount'];
    }
}

// Set page title
$page_title = 'Cheque Deposits';

// Include header
require_once __DIR__ . '/../shared/header.php';
?>

<!-- Include Cheques CSS -->
<link rel="stylesheet" href="cheques.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once __DIR__ . '/../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once __DIR__ . '/../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1; width: 100%;">`
        <!-- Cheques Hero Section -->
        <div class="cheques-hero mb-4" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); border-radius: 12px; padding: 2rem; color: white;">
            <div class="hero-content" style="display: flex; justify-content: space-between; align-items: center; gap: 2rem;">
                <div class="hero-main" style="flex: 1;">
                    <div class="hero-title" style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem;">Cheque Deposits</div>
                    <div class="hero-subtitle" style="font-size: 1.125rem; opacity: 0.9; margin-bottom: 1rem;">View your deposited cheques and their clearance status</div>
                    <div class="hero-stats" style="font-size: 0.875rem; opacity: 0.8;">
                        Account: <?php echo htmlspecialchars($user['account_number']); ?> • Total Deposits: <?php echo $total_cheques; ?> • Cleared Amount: <?php echo formatCurrency($total_cleared_amount, $user['currency']); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-outline-light" onclick="refreshChequeStatus()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh Status
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Total Deposits Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total Deposits</div>
                    <div class="balance-amount" style="color: #3b82f6;">
                        <?php echo $total_cheques; ?>
                    </div>
                    <div class="balance-subtitle">Cheques Deposited</div>
                </div>
            </div>

            <!-- Cleared Cheques Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Cleared</div>
                    <div class="balance-amount" style="color: #10b981;">
                        <?php echo $cleared_cheques; ?>
                    </div>
                    <div class="balance-subtitle">Successfully Processed</div>
                </div>
            </div>

            <!-- Pending Cheques Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Pending</div>
                    <div class="balance-amount" style="color: #f59e0b;">
                        <?php echo $pending_cheques; ?>
                    </div>
                    <div class="balance-subtitle">Awaiting Clearance</div>
                </div>
            </div>

            <!-- Cleared Amount Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Cleared Amount</div>
                    <div class="balance-amount" style="color: #8b5cf6;">
                        <?php echo formatCurrency($total_cleared_amount, $user['currency']); ?>
                    </div>
                    <div class="balance-subtitle">Total Cleared</div>
                </div>
            </div>
        </div>

        <!-- Cheque Deposits Section -->
        <div class="cheque-deposits-section">
            <div class="section-header mb-4">
                <h3><i class="fas fa-file-invoice me-2"></i>Your Deposited Cheques</h3>
                <p class="text-muted">View and track the status of your cheque deposits</p>
            </div>

            <?php if (empty($cheques)): ?>
                <div class="empty-state" style="text-align: center; padding: 3rem; background: white; border-radius: 12px; border: 1px solid var(--border-color);">
                    <div class="empty-icon" style="font-size: 4rem; color: var(--text-muted); margin-bottom: 1rem;">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <h4>No Cheque Deposits Found</h4>
                    <p class="text-muted">You haven't deposited any cheques yet. Contact your bank to deposit cheques via email.</p>
                </div>
            <?php else: ?>
                <!-- Compact Table Layout -->
                <div class="card">
                    <div class="table-responsive">
                        <table class="table table-vcenter table-hover">
                            <thead>
                                <tr>
                                    <th class="w-1">#</th>
                                    <th>Cheque Details</th>
                                    <th>Amount</th>
                                    <th>Sender</th>
                                    <th>Bank</th>
                                    <th>Status</th>
                                    <th>Deposit Date</th>
                                    <th class="w-1">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $row_number = 1;
                                foreach ($cheques as $cheque):
                                ?>
                                <tr>
                                    <!-- Row Number -->
                                    <td>
                                        <span class="text-muted"><?php echo $row_number++; ?></span>
                                    </td>

                                    <!-- Cheque Details -->
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div class="fw-bold">
                                                <i class="fas fa-file-invoice me-1"></i>
                                                #<?php echo htmlspecialchars($cheque['cheque_number']); ?>
                                            </div>
                                            <small class="text-muted"><?php echo ucfirst($cheque['account_type']); ?> • <?php echo htmlspecialchars($cheque['branch_code']); ?></small>
                                        </div>
                                    </td>

                                    <!-- Amount -->
                                    <td>
                                        <span class="fw-bold text-primary">
                                            <?php echo formatCurrency($cheque['amount'], $cheque['currency']); ?>
                                        </span>
                                    </td>

                                    <!-- Sender -->
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div class="fw-bold" style="font-size: 0.9rem;"><?php echo htmlspecialchars($cheque['sender_name']); ?></div>
                                            <small class="text-muted">ID: <?php echo htmlspecialchars($cheque['id_passport_number']); ?></small>
                                        </div>
                                    </td>

                                    <!-- Bank -->
                                    <td>
                                        <div class="text-truncate" style="max-width: 150px;" title="<?php echo htmlspecialchars($cheque['bank_name']); ?>">
                                            <?php echo htmlspecialchars($cheque['bank_name']); ?>
                                        </div>
                                    </td>

                                    <!-- Status -->
                                    <td>
                                        <?php
                                        $status_colors = [
                                            'pending' => 'warning',
                                            'processing' => 'info',
                                            'cleared' => 'success',
                                            'rejected' => 'danger'
                                        ];
                                        $status_color = $status_colors[$cheque['clearance_status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                            <?php echo ucfirst($cheque['clearance_status']); ?>
                                        </span>
                                    </td>

                                    <!-- Deposit Date -->
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div style="font-size: 0.9rem;"><?php echo date('M j, Y', strtotime($cheque['deposit_date'])); ?></div>
                                            <small class="text-muted"><?php echo date('g:i A', strtotime($cheque['created_at'])); ?></small>
                                        </div>
                                    </td>

                                    <!-- Actions -->
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="previewCheque('<?php echo $cheque['id']; ?>')" title="Preview Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($cheque['clearance_status'] === 'cleared'): ?>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadReceipt('<?php echo $cheque['id']; ?>')" title="Download Receipt">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <?php endif; ?>
                                            <?php if (!empty($cheque['cheque_image_path'])): ?>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="viewChequeImage('<?php echo htmlspecialchars($cheque['cheque_image_path']); ?>')" title="View Image">
                                                <i class="fas fa-image"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>
        </div>



        </div> <!-- End Content Container -->

    </div> <!-- End Main Content -->

    <!-- User Footer Component -->
    <?php require_once __DIR__ . '/../shared/user_footer.php'; ?>
</div>
<!-- Cheque Preview Modal -->
<div class="modal fade" id="chequePreviewModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-invoice me-2"></i>Cheque Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="chequePreviewContent">
                <!-- Cheque preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script src="cheques.js"></script>
