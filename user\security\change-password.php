<?php
/**
 * Change Password Handler
 * Process password change requests from security settings
 */

session_start();
header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit();
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

require_once __DIR__ . '/../../config/config.php';

try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get form data
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validate inputs
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        echo json_encode(['success' => false, 'message' => 'All fields are required']);
        exit();
    }
    
    if ($new_password !== $confirm_password) {
        echo json_encode(['success' => false, 'message' => 'New passwords do not match']);
        exit();
    }
    
    // Validate password strength
    if (strlen($new_password) < 8) {
        echo json_encode(['success' => false, 'message' => 'Password must be at least 8 characters long']);
        exit();
    }
    
    if (!preg_match('/[A-Z]/', $new_password)) {
        echo json_encode(['success' => false, 'message' => 'Password must contain at least one uppercase letter']);
        exit();
    }
    
    if (!preg_match('/[a-z]/', $new_password)) {
        echo json_encode(['success' => false, 'message' => 'Password must contain at least one lowercase letter']);
        exit();
    }
    
    if (!preg_match('/\d/', $new_password)) {
        echo json_encode(['success' => false, 'message' => 'Password must contain at least one number']);
        exit();
    }
    
    if (!preg_match('/[!@#$%^&*(),.?":{}|<>]/', $new_password)) {
        echo json_encode(['success' => false, 'message' => 'Password must contain at least one special character']);
        exit();
    }
    
    // Get current user data
    $user_query = "SELECT password FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit();
    }
    
    // Verify current password
    if (!verifyPassword($current_password, $user['password'])) {
        echo json_encode(['success' => false, 'message' => 'Current password is incorrect']);
        exit();
    }
    
    // Hash new password
    $new_password_hash = hashPassword($new_password);
    
    // Update password in database
    $update_query = "UPDATE accounts SET password = ?, updated_at = NOW() WHERE id = ?";
    $update_result = $db->query($update_query, [$new_password_hash, $user_id]);
    
    if ($update_result) {
        // Log the password change
        $log_query = "INSERT INTO user_security_history (
            user_id, setting_changed, old_value, new_value, reason, changed_by, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW())";
        
        try {
            $db->query($log_query, [
                $user_id, 
                'password_changed', 
                'hidden', 
                'hidden', 
                'User changed password via security settings', 
                $user_id
            ]);
        } catch (Exception $e) {
            // Log table might not exist, continue without error
            error_log("Security history log error: " . $e->getMessage());
        }
        
        echo json_encode(['success' => true, 'message' => 'Password updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update password']);
    }
    
} catch (Exception $e) {
    error_log("Password change error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while updating password']);
}
?>
