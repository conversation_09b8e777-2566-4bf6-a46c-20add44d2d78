/**
 * IRS Tax Return Assistance Application JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize form functionality
    initializeFormHandlers();
    initializeDependentsHandler();
    initializeEmploymentHandler();
    initializeFormValidation();
});

/**
 * Initialize form event handlers
 */
function initializeFormHandlers() {
    // SSN formatting
    const ssnInput = document.getElementById('ssn');
    if (ssnInput) {
        ssnInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 6) {
                value = value.substring(0, 3) + '-' + value.substring(3, 5) + '-' + value.substring(5, 9);
            } else if (value.length >= 4) {
                value = value.substring(0, 3) + '-' + value.substring(3);
            }
            e.target.value = value;
        });
    }

    // EIN formatting
    const einInput = document.getElementById('employer_ein');
    if (einInput) {
        einInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 3) {
                value = value.substring(0, 2) + '-' + value.substring(2, 9);
            }
            e.target.value = value;
        });
    }

    // Phone number formatting
    const phoneInput = document.getElementById('phone_number');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 7) {
                value = '(' + value.substring(0, 3) + ') ' + value.substring(3, 6) + '-' + value.substring(6, 10);
            } else if (value.length >= 4) {
                value = '(' + value.substring(0, 3) + ') ' + value.substring(3);
            } else if (value.length >= 1) {
                value = '(' + value;
            }
            e.target.value = value;
        });
    }

    // Routing number validation
    const routingInput = document.getElementById('routing_number');
    if (routingInput) {
        routingInput.addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '').substring(0, 9);
        });
    }
}

/**
 * Initialize dependents handler
 */
function initializeDependentsHandler() {
    const dependentsSelect = document.getElementById('number_of_dependents');
    const dependentsContainer = document.getElementById('dependentsContainer');

    if (dependentsSelect && dependentsContainer) {
        dependentsSelect.addEventListener('change', function() {
            const numDependents = parseInt(this.value) || 0;
            generateDependentFields(numDependents, dependentsContainer);
        });
    }
}

/**
 * Generate dependent input fields
 */
function generateDependentFields(numDependents, container) {
    container.innerHTML = '';

    for (let i = 1; i <= numDependents; i++) {
        const dependentDiv = document.createElement('div');
        dependentDiv.className = 'dependent-group';
        dependentDiv.innerHTML = `
            <div class="dependent-header">
                <i class="fas fa-user"></i>
                <span>Dependent ${i}</span>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="dependent_name_${i}" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="dependent_name_${i}" name="dependent_name_${i}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="dependent_relationship_${i}" class="form-label">Relationship</label>
                        <select class="form-select" id="dependent_relationship_${i}" name="dependent_relationship_${i}">
                            <option value="">Select Relationship</option>
                            <option value="child">Child</option>
                            <option value="spouse">Spouse</option>
                            <option value="parent">Parent</option>
                            <option value="sibling">Sibling</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="dependent_ssn_${i}" class="form-label">SSN</label>
                        <input type="text" class="form-control dependent-ssn" id="dependent_ssn_${i}" name="dependent_ssn_${i}" 
                               placeholder="XXX-XX-XXXX" pattern="[0-9]{3}-[0-9]{2}-[0-9]{4}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="dependent_dob_${i}" class="form-label">Date of Birth</label>
                        <input type="date" class="form-control" id="dependent_dob_${i}" name="dependent_dob_${i}">
                    </div>
                </div>
            </div>
        `;
        container.appendChild(dependentDiv);

        // Add SSN formatting to dependent SSN fields
        const dependentSsnInput = dependentDiv.querySelector('.dependent-ssn');
        if (dependentSsnInput) {
            dependentSsnInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 6) {
                    value = value.substring(0, 3) + '-' + value.substring(3, 5) + '-' + value.substring(5, 9);
                } else if (value.length >= 4) {
                    value = value.substring(0, 3) + '-' + value.substring(3);
                }
                e.target.value = value;
            });
        }
    }
}

/**
 * Initialize employment type handler
 */
function initializeEmploymentHandler() {
    const employmentSelect = document.getElementById('employment_type');
    const employerFields = document.getElementById('employerFields');

    if (employmentSelect && employerFields) {
        employmentSelect.addEventListener('change', function() {
            const showEmployerFields = ['employed', 'self_employed'].includes(this.value);
            employerFields.style.display = showEmployerFields ? 'flex' : 'none';
            
            // Update required status
            const employerNameInput = document.getElementById('employer_name');
            const employerEinInput = document.getElementById('employer_ein');
            
            if (showEmployerFields && this.value === 'employed') {
                employerNameInput.required = true;
                employerEinInput.required = true;
            } else {
                employerNameInput.required = false;
                employerEinInput.required = false;
            }
        });
    }
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const form = document.getElementById('irsApplicationForm');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                showValidationErrors();
            } else {
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
                }
            }
        });

        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    }
}

/**
 * Validate entire form
 */
function validateForm() {
    const form = document.getElementById('irsApplicationForm');
    let isValid = true;

    // Validate required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });

    // Custom validations
    if (!validateSSN(document.getElementById('ssn'))) {
        isValid = false;
    }

    if (!validateEmail(document.getElementById('email'))) {
        isValid = false;
    }

    if (!validateRoutingNumber(document.getElementById('routing_number'))) {
        isValid = false;
    }

    return isValid;
}

/**
 * Validate individual field
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // Check if required field is empty
    if (field.required && !value) {
        isValid = false;
        errorMessage = 'This field is required.';
    }

    // Field-specific validations
    if (value && field.type === 'email' && !isValidEmail(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address.';
    }

    if (value && field.id === 'ssn' && !isValidSSN(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid SSN (XXX-XX-XXXX).';
    }

    if (value && field.id === 'routing_number' && !isValidRoutingNumber(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid 9-digit routing number.';
    }

    // Update field appearance
    field.classList.remove('is-valid', 'is-invalid');
    field.classList.add(isValid ? 'is-valid' : 'is-invalid');

    // Show/hide error message
    let feedback = field.parentNode.querySelector('.invalid-feedback');
    if (!isValid) {
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentNode.appendChild(feedback);
        }
        feedback.textContent = errorMessage;
    } else if (feedback) {
        feedback.remove();
    }

    return isValid;
}

/**
 * Validation helper functions
 */
function validateSSN(field) {
    return field && isValidSSN(field.value);
}

function validateEmail(field) {
    return field && isValidEmail(field.value);
}

function validateRoutingNumber(field) {
    return field && isValidRoutingNumber(field.value);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidSSN(ssn) {
    const ssnRegex = /^\d{3}-\d{2}-\d{4}$/;
    return ssnRegex.test(ssn);
}

function isValidRoutingNumber(routing) {
    return /^\d{9}$/.test(routing);
}

/**
 * Show validation errors
 */
function showValidationErrors() {
    const firstInvalidField = document.querySelector('.is-invalid');
    if (firstInvalidField) {
        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstInvalidField.focus();
    }
}

/**
 * Utility function to format currency
 */
function formatCurrency(input) {
    let value = input.value.replace(/[^\d.]/g, '');
    if (value) {
        value = parseFloat(value).toFixed(2);
        input.value = value;
    }
}
