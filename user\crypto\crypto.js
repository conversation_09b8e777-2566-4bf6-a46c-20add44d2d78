// Crypto Transfer JavaScript

document.addEventListener('DOMContentLoaded', function() {
    const cryptoTypeSelect = document.getElementById('crypto_type');
    const amountInput = document.getElementById('amount');
    const cryptoSymbol = document.getElementById('crypto-symbol');
    const recipientAddressInput = document.getElementById('recipient_address');
    const transferForm = document.getElementById('cryptoTransferForm');

    // Update crypto symbol when type changes
    if (cryptoTypeSelect && cryptoSymbol) {
        cryptoTypeSelect.addEventListener('change', function() {
            const selectedCrypto = this.value;
            cryptoSymbol.textContent = selectedCrypto || 'CRYPTO';
            
            // Update amount input placeholder and limits based on crypto type
            if (amountInput) {
                switch(selectedCrypto) {
                    case 'BTC':
                        amountInput.placeholder = 'e.g., 0.001';
                        amountInput.step = '0.00000001';
                        break;
                    case 'ETH':
                        amountInput.placeholder = 'e.g., 0.01';
                        amountInput.step = '0.000000001';
                        break;
                    case 'LTC':
                        amountInput.placeholder = 'e.g., 0.1';
                        amountInput.step = '0.00000001';
                        break;
                    case 'XRP':
                        amountInput.placeholder = 'e.g., 10';
                        amountInput.step = '0.000001';
                        break;
                    case 'ADA':
                        amountInput.placeholder = 'e.g., 5';
                        amountInput.step = '0.000001';
                        break;
                    case 'DOT':
                        amountInput.placeholder = 'e.g., 1';
                        amountInput.step = '0.0000000001';
                        break;
                    default:
                        amountInput.placeholder = 'Enter amount';
                        amountInput.step = '0.00000001';
                }
            }
        });
    }

    // Validate recipient address format
    if (recipientAddressInput) {
        recipientAddressInput.addEventListener('blur', function() {
            const address = this.value.trim();
            const cryptoType = cryptoTypeSelect ? cryptoTypeSelect.value : '';
            
            if (address && cryptoType) {
                validateAddress(address, cryptoType, this);
            }
        });

        recipientAddressInput.addEventListener('input', function() {
            // Remove any validation classes while typing
            this.classList.remove('is-valid', 'is-invalid');
            const feedback = this.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.remove();
            }
        });
    }

    // Form submission handling
    if (transferForm) {
        transferForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (validateForm()) {
                showLoadingState();
                // Submit the form after a short delay to show loading state
                setTimeout(() => {
                    this.submit();
                }, 500);
            }
        });
    }

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});

// Validate cryptocurrency address format
function validateAddress(address, cryptoType, inputElement) {
    let isValid = false;
    let errorMessage = '';

    // Basic validation patterns for different crypto types
    const patterns = {
        'BTC': /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/,
        'ETH': /^0x[a-fA-F0-9]{40}$/,
        'LTC': /^[LM3][a-km-zA-HJ-NP-Z1-9]{26,33}$|^ltc1[a-z0-9]{39,59}$/,
        'XRP': /^r[0-9a-zA-Z]{24,34}$/,
        'ADA': /^addr1[a-z0-9]{98}$/,
        'DOT': /^1[a-zA-Z0-9]{46}$/
    };

    if (patterns[cryptoType]) {
        isValid = patterns[cryptoType].test(address);
        if (!isValid) {
            errorMessage = `Invalid ${cryptoType} address format`;
        }
    } else {
        // Generic validation for unknown crypto types
        isValid = address.length >= 26 && address.length <= 62;
        if (!isValid) {
            errorMessage = 'Address must be between 26 and 62 characters';
        }
    }

    // Update input styling
    if (isValid) {
        inputElement.classList.remove('is-invalid');
        inputElement.classList.add('is-valid');
        removeFeedback(inputElement);
    } else {
        inputElement.classList.remove('is-valid');
        inputElement.classList.add('is-invalid');
        showFeedback(inputElement, errorMessage, 'invalid');
    }

    return isValid;
}

// Show validation feedback
function showFeedback(inputElement, message, type) {
    removeFeedback(inputElement);
    
    const feedback = document.createElement('div');
    feedback.className = `${type}-feedback`;
    feedback.textContent = message;
    
    inputElement.parentNode.appendChild(feedback);
}

// Remove validation feedback
function removeFeedback(inputElement) {
    const existingFeedback = inputElement.parentNode.querySelector('.invalid-feedback, .valid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
}

// Validate entire form
function validateForm() {
    const form = document.getElementById('cryptoTransferForm');
    const cryptoType = document.getElementById('crypto_type').value;
    const amount = parseFloat(document.getElementById('amount').value);
    const recipientAddress = document.getElementById('recipient_address').value.trim();

    let isValid = true;

    // Validate crypto type
    if (!cryptoType) {
        showFormError('crypto_type', 'Please select a cryptocurrency type');
        isValid = false;
    }

    // Validate amount
    if (!amount || amount <= 0) {
        showFormError('amount', 'Please enter a valid amount');
        isValid = false;
    } else if (amount < 0.001) {
        showFormError('amount', 'Minimum amount is 0.001');
        isValid = false;
    } else if (amount > 100) {
        showFormError('amount', 'Maximum amount is 100 per transaction');
        isValid = false;
    }

    // Validate recipient address
    if (!recipientAddress) {
        showFormError('recipient_address', 'Please enter a recipient address');
        isValid = false;
    } else if (!validateAddress(recipientAddress, cryptoType, document.getElementById('recipient_address'))) {
        isValid = false;
    }

    return isValid;
}

// Show form error
function showFormError(fieldId, message) {
    const field = document.getElementById(fieldId);
    field.classList.add('is-invalid');
    showFeedback(field, message, 'invalid');
}

// Show loading state on form submission
function showLoadingState() {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.classList.add('btn-loading');
        submitBtn.disabled = true;
    }
}

// Reset form function
function resetForm() {
    const form = document.getElementById('cryptoTransferForm');
    if (form) {
        form.reset();
        
        // Remove all validation classes
        const inputs = form.querySelectorAll('.form-control, .form-select');
        inputs.forEach(input => {
            input.classList.remove('is-valid', 'is-invalid');
            removeFeedback(input);
        });
        
        // Reset crypto symbol
        const cryptoSymbol = document.getElementById('crypto-symbol');
        if (cryptoSymbol) {
            cryptoSymbol.textContent = 'CRYPTO';
        }
        
        // Reset amount placeholder
        const amountInput = document.getElementById('amount');
        if (amountInput) {
            amountInput.placeholder = 'Enter amount';
            amountInput.step = '0.00000001';
        }
    }
}

// Format number with appropriate decimal places
function formatCryptoAmount(amount, cryptoType) {
    const decimals = {
        'BTC': 8,
        'ETH': 9,
        'LTC': 8,
        'XRP': 6,
        'ADA': 6,
        'DOT': 10
    };
    
    const decimalPlaces = decimals[cryptoType] || 8;
    return parseFloat(amount).toFixed(decimalPlaces);
}

// Copy text to clipboard
function copyToClipboard(text, element) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const originalText = element.textContent;
        element.textContent = 'Copied!';
        element.classList.add('text-success');
        
        setTimeout(() => {
            element.textContent = originalText;
            element.classList.remove('text-success');
        }, 2000);
    }).catch(function(err) {
        console.error('Failed to copy text: ', err);
    });
}

// Generate QR code for address (if QR library is available)
function generateQRCode(address, containerId) {
    if (typeof QRCode !== 'undefined') {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = '';
            new QRCode(container, {
                text: address,
                width: 128,
                height: 128,
                colorDark: "#000000",
                colorLight: "#ffffff"
            });
        }
    }
}
