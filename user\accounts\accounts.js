/**
 * Internal Transfers Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Accounts page loaded');
    initializeAccountsPage();
    initializeAccountOptions();
});

/**
 * Initialize accounts page functionality
 */
function initializeAccountsPage() {
    // Initialize form handlers
    initializeTransferForm();
    
    // Add hover effects to balance cards
    const balanceCards = document.querySelectorAll('.balance-card');
    balanceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
            this.style.boxShadow = '0 12px 40px rgba(0, 0, 0, 0.2)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
        });
    });

    // Add hover effects to transfer items
    const transferItems = document.querySelectorAll('.transfer-item');
    transferItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(4px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
}

/**
 * Initialize transfer form
 */
function initializeTransferForm() {
    const form = document.getElementById('internalTransferForm');
    const fromAccount = document.getElementById('fromAccount');
    const toAccount = document.getElementById('toAccount');
    const amountInput = document.getElementById('transferAmount');
    
    // Handle account selection changes
    fromAccount.addEventListener('change', function() {
        updateToAccountOptions();
        updateTransferSummary();
    });
    
    toAccount.addEventListener('change', function() {
        updateTransferSummary();
    });
    
    amountInput.addEventListener('input', function() {
        updateTransferSummary();
        validateAmount();
    });
    
    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        handleTransferSubmission();
    });
}

// Store original options on page load
let originalToAccountOptions = [];

/**
 * Initialize account options
 */
function initializeAccountOptions() {
    const toAccount = document.getElementById('toAccount');
    originalToAccountOptions = Array.from(toAccount.querySelectorAll('option')).map(option => ({
        value: option.value,
        text: option.textContent
    }));
}

/**
 * Update destination account options based on source selection
 */
function updateToAccountOptions() {
    const fromAccount = document.getElementById('fromAccount').value;
    const toAccount = document.getElementById('toAccount');

    // Reset destination options
    toAccount.innerHTML = '<option value="">Select destination account</option>';

    // Add options excluding the selected from account
    originalToAccountOptions.forEach(option => {
        if (option.value && option.value !== fromAccount && option.value !== '') {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            toAccount.appendChild(optionElement);
        }
    });

    // Auto-select if only one option available
    const availableOptions = toAccount.querySelectorAll('option:not([value=""])');
    if (availableOptions.length === 1) {
        toAccount.value = availableOptions[0].value;
    }
}

/**
 * Set transfer amount
 */
function setAmount(amount) {
    document.getElementById('transferAmount').value = amount;
    updateTransferSummary();
    validateAmount();
}

/**
 * Set maximum available amount
 */
function setMaxAmount() {
    const fromAccountSelect = document.getElementById('fromAccount');
    const selectedOption = fromAccountSelect.options[fromAccountSelect.selectedIndex];
    const fromAccount = fromAccountSelect.value;
    let maxAmount = 0;

    if (fromAccount === 'main_account') {
        maxAmount = window.userData.mainBalance;
    } else if (fromAccount.startsWith('virtual_card_')) {
        maxAmount = parseFloat(selectedOption.getAttribute('data-balance')) || 0;
    }

    if (maxAmount > 0) {
        document.getElementById('transferAmount').value = maxAmount.toFixed(2);
        updateTransferSummary();
        validateAmount();
    } else {
        showNotification('No funds available in selected account', 'error');
    }
}

/**
 * Validate transfer amount
 */
function validateAmount() {
    const fromAccountSelect = document.getElementById('fromAccount');
    const selectedOption = fromAccountSelect.options[fromAccountSelect.selectedIndex];
    const fromAccount = fromAccountSelect.value;
    const amount = parseFloat(document.getElementById('transferAmount').value) || 0;
    const amountInput = document.getElementById('transferAmount');

    let maxAmount = 0;
    if (fromAccount === 'main_account') {
        maxAmount = window.userData.mainBalance;
    } else if (fromAccount.startsWith('virtual_card_')) {
        maxAmount = parseFloat(selectedOption.getAttribute('data-balance')) || 0;
    }

    if (amount > maxAmount) {
        amountInput.setCustomValidity('Amount exceeds available balance');
        amountInput.classList.add('is-invalid');
        return false;
    } else if (amount <= 0) {
        amountInput.setCustomValidity('Amount must be greater than 0');
        amountInput.classList.add('is-invalid');
        return false;
    } else {
        amountInput.setCustomValidity('');
        amountInput.classList.remove('is-invalid');
        return true;
    }
}

/**
 * Update transfer summary
 */
function updateTransferSummary() {
    const fromAccountSelect = document.getElementById('fromAccount');
    const toAccountSelect = document.getElementById('toAccount');
    const fromAccount = fromAccountSelect.value;
    const toAccount = toAccountSelect.value;
    const amount = parseFloat(document.getElementById('transferAmount').value) || 0;
    const summary = document.getElementById('transferSummary');

    if (fromAccount && toAccount && amount > 0) {
        const fromOption = fromAccountSelect.options[fromAccountSelect.selectedIndex];
        const toOption = toAccountSelect.options[toAccountSelect.selectedIndex];

        const fromText = fromOption.text;
        const toText = toOption.text;

        document.getElementById('summaryFrom').textContent = fromText;
        document.getElementById('summaryTo').textContent = toText;
        document.getElementById('summaryAmount').textContent = formatCurrency(amount, window.userData.currency);

        summary.style.display = 'block';
    } else {
        summary.style.display = 'none';
    }
}

/**
 * Handle transfer form submission
 */
function handleTransferSubmission() {
    const form = document.getElementById('internalTransferForm');
    const formData = new FormData(form);
    
    // Validate form
    if (!form.checkValidity() || !validateAmount()) {
        form.reportValidity();
        return;
    }
    
    // Show confirmation modal
    showTransferConfirmation(formData);
}

/**
 * Show transfer confirmation modal
 */
function showTransferConfirmation(formData) {
    const fromAccountSelect = document.getElementById('fromAccount');
    const toAccountSelect = document.getElementById('toAccount');

    const fromAccount = formData.get('from_account');
    const toAccount = formData.get('to_account');
    const amount = parseFloat(formData.get('amount'));
    const note = formData.get('note') || 'Internal transfer';

    const fromText = fromAccountSelect.options[fromAccountSelect.selectedIndex].text;
    const toText = toAccountSelect.options[toAccountSelect.selectedIndex].text;

    const confirmationContent = document.getElementById('confirmationContent');
    confirmationContent.innerHTML = `
        <div class="transfer-summary-card">
            <div class="transfer-detail-row">
                <div class="transfer-detail-label">From:</div>
                <div class="transfer-detail-value">${fromText}</div>
            </div>
            <div class="transfer-detail-row">
                <div class="transfer-detail-label">To:</div>
                <div class="transfer-detail-value">${toText}</div>
            </div>
            <div class="transfer-detail-row">
                <div class="transfer-detail-label">Amount:</div>
                <div class="transfer-detail-value transfer-amount-highlight">${formatCurrency(amount, window.userData.currency)}</div>
            </div>
            <div class="transfer-detail-row">
                <div class="transfer-detail-label">Note:</div>
                <div class="transfer-detail-value">${note}</div>
            </div>
            <div class="transfer-detail-row">
                <div class="transfer-detail-label">Transfer Fee:</div>
                <div class="transfer-detail-value transfer-fee-free">FREE</div>
            </div>
        </div>
        <div class="transfer-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <div class="transfer-warning-text">
                This transfer will be processed immediately and cannot be reversed.
            </div>
        </div>
    `;

    // Set up confirmation button
    const confirmBtn = document.getElementById('confirmTransferBtn');
    confirmBtn.onclick = () => processTransfer(formData);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('transferConfirmModal'));
    modal.show();
}

/**
 * Process the transfer
 */
async function processTransfer(formData) {
    const confirmBtn = document.getElementById('confirmTransferBtn');
    const originalText = confirmBtn.textContent;
    
    try {
        // Show loading state
        confirmBtn.classList.add('loading');
        confirmBtn.disabled = true;
        
        // Simulate API call (replace with actual endpoint)
        const response = await fetch('process-transfer.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('transferConfirmModal')).hide();
            
            // Show success message
            showNotification('Transfer completed successfully!', 'success');
            
            // Reset form
            resetForm();
            
            // Update balances (you might want to refresh the page or update via AJAX)
            setTimeout(() => {
                window.location.reload();
            }, 2000);
            
        } else {
            throw new Error(result.message || 'Transfer failed');
        }
        
    } catch (error) {
        console.error('Transfer error:', error);
        showNotification(error.message || 'Transfer failed. Please try again.', 'error');
    } finally {
        // Reset button state
        confirmBtn.classList.remove('loading');
        confirmBtn.disabled = false;
        confirmBtn.textContent = originalText;
    }
}

/**
 * Transfer from specific account
 */
function transferFrom(accountType) {
    document.getElementById('fromAccount').value = accountType;
    updateToAccountOptions();
    
    // Scroll to transfer form
    document.querySelector('.transfer-section').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

/**
 * Reset transfer form
 */
function resetForm() {
    const form = document.getElementById('internalTransferForm');
    form.reset();
    
    // Reset custom validation
    const amountInput = document.getElementById('transferAmount');
    amountInput.setCustomValidity('');
    amountInput.classList.remove('is-invalid');
    
    // Hide summary
    document.getElementById('transferSummary').style.display = 'none';
    
    // Reset destination options
    const toAccount = document.getElementById('toAccount');
    toAccount.innerHTML = '<option value="">Select destination account</option>';
}

/**
 * View transfer history
 */
function viewTransferHistory() {
    window.location.href = '../transactions/?type=internal';
}

/**
 * Format currency
 */
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
    `;
    
    // Set background color based on type
    switch(type) {
        case 'success':
            notification.style.background = '#28a745';
            break;
        case 'error':
            notification.style.background = '#dc3545';
            break;
        default:
            notification.style.background = '#17a2b8';
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 4000);
}
