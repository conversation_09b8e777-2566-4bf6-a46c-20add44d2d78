<?php
/**
 * Crypto Deposit Form
 * Allow users to initiate cryptocurrency deposits to their account
 */

// Set page variables
$page_title = 'Crypto Deposit';
$current_page = 'crypto';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Check if crypto deposits feature is enabled
$crypto_enabled_query = "SELECT value FROM admin_settings WHERE setting_key = 'crypto_deposits_enabled'";
$crypto_enabled_result = $db->query($crypto_enabled_query);
$crypto_enabled = $crypto_enabled_result->fetch_assoc()['value'] ?? '0';

if ($crypto_enabled !== '1') {
    header('Location: ../dashboard/index.php');
    exit();
}

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $crypto_type = trim($_POST['crypto_type']);
        $amount = floatval($_POST['amount']);
        $deposit_method = trim($_POST['deposit_method']);
        $receipt_file = null;
        $deposit_note = trim($_POST['deposit_note'] ?? '');
        
        // Validation
        if (empty($crypto_type) || empty($deposit_method) || $amount <= 0) {
            throw new Exception("Please fill in all required fields with valid values.");
        }
        
        // Validate crypto type
        $valid_crypto_types = ['BTC', 'ETH', 'LTC', 'XRP', 'ADA', 'DOT'];
        if (!in_array($crypto_type, $valid_crypto_types)) {
            throw new Exception("Invalid cryptocurrency type selected.");
        }
        
        // Validate deposit method
        $valid_methods = ['bank_transfer', 'credit_card', 'debit_card', 'external_wallet'];
        if (!in_array($deposit_method, $valid_methods)) {
            throw new Exception("Invalid deposit method selected.");
        }
        
        // Validate amount (minimum and maximum limits)
        if ($amount < 0.001) {
            throw new Exception("Minimum deposit amount is 0.001 " . $crypto_type);
        }
        if ($amount > 1000) {
            throw new Exception("Maximum deposit amount is 1000 " . $crypto_type . " per transaction.");
        }
        
        // Handle file upload for receipt
        if (isset($_FILES['receipt_file']) && $_FILES['receipt_file']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../../uploads/crypto_receipts/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = pathinfo($_FILES['receipt_file']['name'], PATHINFO_EXTENSION);
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'pdf'];
            
            if (!in_array(strtolower($file_extension), $allowed_extensions)) {
                throw new Exception("Invalid file type. Please upload JPG, PNG, or PDF files only.");
            }
            
            if ($_FILES['receipt_file']['size'] > 5 * 1024 * 1024) { // 5MB limit
                throw new Exception("File size too large. Maximum size is 5MB.");
            }
            
            $receipt_file = 'receipt_' . $user_id . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $receipt_file;
            
            if (!move_uploaded_file($_FILES['receipt_file']['tmp_name'], $upload_path)) {
                throw new Exception("Failed to upload receipt file.");
            }
        }
        
        // Generate deposit reference
        $deposit_reference = 'DEP_' . strtoupper(bin2hex(random_bytes(8)));
        
        // Insert crypto deposit request
        $insert_query = "INSERT INTO crypto_deposits (
            account_id, cryptocurrency, deposit_amount, deposit_method,
            deposit_reference, receipt_file_path, deposit_note, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";

        $deposit_id = $db->insert($insert_query, [
            $user_id, $crypto_type, $amount, $deposit_method,
            $deposit_reference, $receipt_file, $deposit_note
        ]);
        
        if ($deposit_id) {
            $success_message = "Crypto deposit request submitted successfully! Reference: " . $deposit_reference;
        } else {
            throw new Exception("Failed to submit deposit request. Please try again.");
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get recent deposits for user
$recent_deposits_query = "SELECT * FROM crypto_deposits WHERE account_id = ? ORDER BY created_at DESC LIMIT 5";
$recent_deposits_result = $db->query($recent_deposits_query, [$user_id]);
$recent_deposits = [];
while ($row = $recent_deposits_result->fetch_assoc()) {
    $recent_deposits[] = $row;
}

// Get admin crypto addresses for display
$admin_addresses_query = "SELECT * FROM admin_crypto_addresses WHERE is_active = 1 ORDER BY cryptocurrency";
$admin_addresses_result = $db->query($admin_addresses_query);
$admin_addresses = [];
while ($row = $admin_addresses_result->fetch_assoc()) {
    $admin_addresses[$row['cryptocurrency']] = $row;
}

// Get available payment methods
$payment_methods = [];
try {
    $payment_methods_query = "SELECT * FROM payment_methods WHERE is_active = 1 ORDER BY method_name";
    $payment_methods_result = $db->query($payment_methods_query);
    if ($payment_methods_result) {
        while ($row = $payment_methods_result->fetch_assoc()) {
            $payment_methods[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Payment methods query error: " . $e->getMessage());
    // Continue with empty array
}
// Set page title
$page_title = 'Crypto Deposit';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Crypto CSS -->
<link rel="stylesheet" href="crypto.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content">

        <!-- Crypto Deposit Hero Section -->
        <div class="crypto-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Cryptocurrency Deposit</div>
                    <div class="hero-subtitle">Deposit cryptocurrency to your account (Simulation)</div>
                    <div class="hero-stats">
                        Available Currencies: <?php echo count($admin_addresses); ?> • Recent Deposits: <?php echo count($recent_deposits); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="transfer.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-paper-plane me-2"></i>Transfer Crypto
                    </a>
                    <a href="history.php" class="btn btn-primary">
                        <i class="fas fa-history me-2"></i>History
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="crypto-stats mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-value"><?php echo count($admin_addresses); ?></div>
                    <div class="stat-label">Supported Currencies</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-download"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-value"><?php echo count($recent_deposits); ?></div>
                    <div class="stat-label">Recent Deposits</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-value">24h</div>
                    <div class="stat-label">Processing Time</div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Deposit Form -->
        <div class="row">
            <div class="col-lg-8">
                    <div class="card crypto-card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-download me-2"></i>
                                New Crypto Deposit
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="" enctype="multipart/form-data" id="cryptoDepositForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="crypto_type" class="form-label">Cryptocurrency Type</label>
                                            <select class="form-select" id="crypto_type" name="crypto_type" required>
                                                <option value="">Select Cryptocurrency</option>
                                                <option value="BTC">Bitcoin (BTC)</option>
                                                <option value="ETH">Ethereum (ETH)</option>
                                                <option value="LTC">Litecoin (LTC)</option>
                                                <option value="XRP">Ripple (XRP)</option>
                                                <option value="ADA">Cardano (ADA)</option>
                                                <option value="DOT">Polkadot (DOT)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="amount" name="amount" 
                                                       step="0.00000001" min="0.001" max="1000" required>
                                                <span class="input-group-text" id="crypto-symbol">CRYPTO</span>
                                            </div>
                                            <div class="form-text">Minimum: 0.001, Maximum: 1000 per transaction</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="deposit_method" class="form-label">Deposit Method</label>
                                    <select class="form-select" id="deposit_method" name="deposit_method" required>
                                        <option value="">Select Deposit Method</option>
                                        <?php foreach ($payment_methods as $method): ?>
                                        <option value="<?php echo $method['id']; ?>" data-method-details='<?php echo json_encode($method); ?>'>
                                            <?php echo htmlspecialchars($method['method_name']); ?>
                                            <?php if ($method['processing_fee'] > 0): ?>
                                            (Fee: <?php echo $method['fee_type'] === 'percentage' ? $method['processing_fee'] . '%' : '$' . number_format($method['processing_fee'], 2); ?>)
                                            <?php endif; ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="receipt_file" class="form-label">Upload Receipt/Proof (Optional)</label>
                                    <input type="file" class="form-control" id="receipt_file" name="receipt_file" 
                                           accept=".jpg,.jpeg,.png,.pdf">
                                    <div class="form-text">Upload proof of payment (JPG, PNG, or PDF, max 5MB)</div>
                                </div>

                                <div class="mb-3">
                                    <label for="deposit_note" class="form-label">Deposit Note (Optional)</label>
                                    <textarea class="form-control" id="deposit_note" name="deposit_note" rows="3" 
                                              placeholder="Add a note about this deposit (optional)"></textarea>
                                </div>

                                <!-- Payment Method Details Display -->
                                <div class="mb-3" id="payment-method-section" style="display: none;">
                                    <label class="form-label">Payment Details</label>
                                    <div class="alert alert-info">
                                        <div id="payment-method-content">
                                            <!-- Payment details will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Admin Addresses Display -->
                                <div class="mb-3" id="admin-address-section" style="display: none;">
                                    <label class="form-label">Our Wallet Address</label>
                                    <div class="alert alert-info">
                                        <div id="admin-address-content">
                                            <!-- Address will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>



                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>Reset Form
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-download me-2"></i>Submit Deposit Request
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Recent Deposits Sidebar -->
                <div class="col-lg-4">
                    <div class="card crypto-card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Deposits
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_deposits)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-3"></i>
                                <p>No recent deposits</p>
                            </div>
                            <?php else: ?>
                            <div class="transfer-list">
                                <?php foreach ($recent_deposits as $deposit): ?>
                                <div class="transfer-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($deposit['deposit_amount'] ?? $deposit['amount']); ?> <?php echo htmlspecialchars($deposit['cryptocurrency'] ?? $deposit['crypto_type']); ?></div>
                                            <div class="text-muted small"><?php echo date('M j, Y', strtotime($deposit['created_at'])); ?></div>
                                        </div>
                                        <span class="badge bg-<?php echo $deposit['status'] === 'approved' ? 'success' : ($deposit['status'] === 'declined' ? 'danger' : 'warning'); ?>">
                                            <?php echo ucfirst($deposit['status']); ?>
                                        </span>
                                    </div>
                                    <div class="text-muted small mt-1">
                                        Method: <?php echo ucfirst(str_replace('_', ' ', $deposit['deposit_method'])); ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="history.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<style>
.crypto-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 2rem;
    color: white;
    margin-bottom: 2rem;
}

.crypto-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.crypto-hero .hero-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.crypto-hero .hero-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.crypto-hero .hero-stats {
    font-size: 0.95rem;
    opacity: 0.8;
}

.crypto-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}
</style>

<script src="crypto.js"></script>
<script>
    // Admin addresses data
    const adminAddresses = <?php echo json_encode($admin_addresses); ?>;
    const paymentMethods = <?php echo json_encode($payment_methods); ?>;

    // Update crypto symbol and show admin address when type changes
    document.getElementById('crypto_type').addEventListener('change', function() {
        const selectedCrypto = this.value;
        const cryptoSymbol = document.getElementById('crypto-symbol');
        const addressSection = document.getElementById('admin-address-section');
        const addressContent = document.getElementById('admin-address-content');

        cryptoSymbol.textContent = selectedCrypto || 'CRYPTO';

        if (selectedCrypto && adminAddresses[selectedCrypto]) {
            const address = adminAddresses[selectedCrypto];
            addressContent.innerHTML = `
                <strong>Send ${selectedCrypto} to this address:</strong><br>
                <code class="user-select-all">${address.wallet_address}</code>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('${address.wallet_address}', this)">
                    <i class="fas fa-copy"></i> Copy
                </button>
                <div class="mt-2 small text-muted">
                    <strong>Note:</strong> Send your ${selectedCrypto} from your external wallet to this address.
                    After sending, admin will verify the transaction and credit your account balance.
                </div>
            `;
            addressSection.style.display = 'block';
        } else {
            addressSection.style.display = 'none';
        }
    });

    // Show payment method details when method changes
    document.getElementById('deposit_method').addEventListener('change', function() {
        const selectedMethodId = this.value;
        const paymentSection = document.getElementById('payment-method-section');
        const paymentContent = document.getElementById('payment-method-content');

        if (selectedMethodId) {
            const method = paymentMethods.find(m => m.id == selectedMethodId);
            if (method) {
                let accountDetails = '';
                try {
                    const details = JSON.parse(method.account_details);
                    if (method.method_type === 'bank_account') {
                        accountDetails = `
                            <strong>Bank Transfer Details:</strong><br>
                            <strong>Bank Name:</strong> ${details.bank_name || 'N/A'}<br>
                            <strong>Account Name:</strong> ${details.account_name || 'N/A'}<br>
                            <strong>Account Number:</strong> ${details.account_number || 'N/A'}<br>
                            <strong>Routing Number:</strong> ${details.routing_number || 'N/A'}
                        `;
                    } else if (method.method_type === 'crypto_wallet') {
                        accountDetails = `
                            <strong>Crypto Wallet Details:</strong><br>
                            <strong>Wallet Address:</strong> <code>${details.wallet_address || 'N/A'}</code><br>
                            <strong>Network:</strong> ${details.network || 'N/A'}
                        `;
                    } else {
                        accountDetails = `<strong>Payment Method:</strong> ${method.method_name}`;
                    }
                } catch (e) {
                    accountDetails = `<strong>Payment Method:</strong> ${method.method_name}`;
                }

                paymentContent.innerHTML = `
                    ${accountDetails}
                    <div class="mt-2 small text-muted">
                        <strong>Instructions:</strong> Make your payment using the above details, then submit this form.
                        Admin will verify your payment and credit your crypto balance.
                    </div>
                `;
                paymentSection.style.display = 'block';
            }
        } else {
            paymentSection.style.display = 'none';
        }
    });

    function copyToClipboard(text, button) {
        navigator.clipboard.writeText(text).then(function() {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-success');
            setTimeout(function() {
                button.innerHTML = originalText;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-primary');
            }, 2000);
        });
    }
</script>
