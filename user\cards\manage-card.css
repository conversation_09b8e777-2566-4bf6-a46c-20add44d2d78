/* Manage Card Page Styles */

.card-management-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Card Visual Section */
.card-visual-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e5e7eb;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
}

.virtual-card-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
    min-height: 200px;
    max-width: 400px;
    margin: 0 auto;
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.9;
}

.card-content {
    position: relative;
    z-index: 2;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.card-type {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.card-logo i {
    font-size: 1.5rem;
}

.card-number {
    font-family: 'Courier New', monospace;
    font-size: 1.25rem;
    font-weight: 600;
    letter-spacing: 3px;
    margin-bottom: 1.5rem;
}

.number-group {
    margin-right: 1rem;
}

.card-info {
    display: flex;
    justify-content: space-between;
}

.card-holder, .card-expiry {
    flex: 1;
}

.label {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.25rem;
    opacity: 0.8;
}

.value {
    font-size: 0.875rem;
    font-weight: 600;
}

.card-status-badge {
    text-align: center;
    margin-top: 1rem;
}

.status-indicator {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-blocked {
    background: #fecaca;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.status-inactive {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

/* Card Controls Section */
.card-controls-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e5e7eb;
    margin-bottom: 2rem;
}

.controls-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #e5e7eb;
}

.controls-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
}

.controls-grid {
    padding: 2rem;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    margin-bottom: 1rem;
}

.control-item:last-child {
    margin-bottom: 0;
}

.control-info h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
}

.control-info p {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
}

.control-action .btn {
    min-width: 120px;
    font-weight: 600;
}

/* Transactions Section */
.transactions-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e5e7eb;
    margin-bottom: 2rem;
}

.section-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #e5e7eb;
}

.section-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
}

.transactions-list {
    padding: 1.5rem;
}

.no-transactions {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.no-transactions i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    border-color: var(--primary-color);
    background: #f8f9fa;
}

.transaction-item:last-child {
    margin-bottom: 0;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
}

.transaction-details {
    flex: 1;
}

.transaction-description {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.transaction-date {
    font-size: 0.875rem;
    color: #6b7280;
}

.transaction-amount {
    font-weight: 700;
    font-size: 1rem;
}

.transaction-amount.credit {
    color: #059669;
}

.transaction-amount.debit {
    color: #dc2626;
}

/* Card Stats Section */
.card-stats-section, .quick-actions-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.stats-header, .actions-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #e5e7eb;
}

.stats-header h4, .actions-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
}

.stats-grid {
    padding: 1.5rem;
}

.stat-item {
    margin-bottom: 1.5rem;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
}

.stat-value.balance {
    font-size: 1.25rem;
    color: var(--primary-color);
}

/* Quick Actions */
.actions-list {
    padding: 1rem;
}

.action-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    margin-bottom: 0.75rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.action-item:hover {
    border-color: var(--primary-color);
    background: #f8f9fa;
    text-decoration: none;
    color: inherit;
}

.action-item:last-child {
    margin-bottom: 0;
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
}

.action-info {
    flex: 1;
}

.action-title {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.action-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .control-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .control-action {
        width: 100%;
    }
    
    .control-action .btn {
        width: 100%;
    }
    
    .virtual-card-display {
        max-width: 100%;
    }
    
    .card-management-container {
        padding: 0 0.5rem;
    }
}
