<?php
/**
 * Crypto Transfer Form
 * Allow users to initiate simulated cryptocurrency transfers
 */

// Set page variables
$page_title = 'Crypto Transfer';
$current_page = 'crypto';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Check if crypto transfers feature is enabled
$crypto_enabled_query = "SELECT value FROM admin_settings WHERE setting_key = 'crypto_transfers_enabled'";
$crypto_enabled_result = $db->query($crypto_enabled_query);
$crypto_enabled = $crypto_enabled_result->fetch_assoc()['value'] ?? '0';

if ($crypto_enabled !== '1') {
    header('Location: ../dashboard/index.php');
    exit();
}

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $crypto_type = trim($_POST['crypto_type']);
        $amount = floatval($_POST['amount']);
        $recipient_address = trim($_POST['recipient_address']);
        $transfer_note = trim($_POST['transfer_note'] ?? '');
        
        // Validation
        if (empty($crypto_type) || empty($recipient_address) || $amount <= 0) {
            throw new Exception("Please fill in all required fields with valid values.");
        }
        
        // Validate crypto type
        $valid_crypto_types = ['BTC', 'ETH', 'LTC', 'XRP', 'ADA', 'DOT'];
        if (!in_array($crypto_type, $valid_crypto_types)) {
            throw new Exception("Invalid cryptocurrency type selected.");
        }
        
        // Validate amount (minimum and maximum limits)
        if ($amount < 0.001) {
            throw new Exception("Minimum transfer amount is 0.001 " . $crypto_type);
        }
        if ($amount > 100) {
            throw new Exception("Maximum transfer amount is 100 " . $crypto_type . " per transaction.");
        }
        
        // Validate recipient address format (basic validation)
        if (strlen($recipient_address) < 26 || strlen($recipient_address) > 62) {
            throw new Exception("Invalid recipient address format.");
        }
        
        // Generate simulated transaction hash
        $transaction_hash = 'SIM_' . strtoupper(bin2hex(random_bytes(16)));
        
        // Insert crypto transfer request
        $insert_query = "INSERT INTO crypto_transfers (
            account_id, cryptocurrency, amount, destination_address,
            transaction_hash, transfer_note, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())";

        $transfer_id = $db->insert($insert_query, [
            $user_id, $crypto_type, $amount, $recipient_address,
            $transaction_hash, $transfer_note
        ]);
        
        if ($transfer_id) {
            $success_message = "Crypto transfer request submitted successfully! Transaction ID: " . $transaction_hash;
        } else {
            throw new Exception("Failed to submit transfer request. Please try again.");
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's crypto wallets
$crypto_wallets = [];
$total_crypto_balance = 0;
try {
    $wallets_query = "SELECT * FROM crypto_wallets WHERE account_id = ? AND status = 'active' ORDER BY cryptocurrency";
    $wallets_result = $db->query($wallets_query, [$user_id]);
    if ($wallets_result) {
        while ($row = $wallets_result->fetch_assoc()) {
            $crypto_wallets[] = $row;
            $total_crypto_balance += $row['usd_equivalent'] ?? 0;
        }
    }
} catch (Exception $e) {
    error_log("Crypto wallets query error: " . $e->getMessage());
    // Continue with empty array
}

// Get recent transfers for user
$recent_transfers_query = "SELECT * FROM crypto_transfers WHERE account_id = ? ORDER BY created_at DESC LIMIT 5";
$recent_transfers_result = $db->query($recent_transfers_query, [$user_id]);
$recent_transfers = [];
while ($row = $recent_transfers_result->fetch_assoc()) {
    $recent_transfers[] = $row;
}
// Set page title
$page_title = 'Crypto Transfer';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Crypto CSS -->
<link rel="stylesheet" href="crypto.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content">

        <!-- Crypto Transfer Hero Section -->
        <div class="crypto-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Cryptocurrency Transfer</div>
                    <div class="hero-subtitle">Send cryptocurrency to external wallets (Simulation)</div>
                    <div class="hero-stats">
                        Available Wallets: <?php echo count($crypto_wallets); ?> • Total Balance: <?php echo number_format($total_crypto_balance, 2); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="deposit.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-plus me-2"></i>Deposit Crypto
                    </a>
                    <a href="history.php" class="btn btn-primary">
                        <i class="fas fa-history me-2"></i>Transfer History
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="crypto-stats mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-value"><?php echo count($crypto_wallets); ?></div>
                    <div class="stat-label">Active Wallets</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-value"><?php echo number_format($total_crypto_balance, 2); ?></div>
                    <div class="stat-label">Total Balance</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-value"><?php echo count($recent_transfers); ?></div>
                    <div class="stat-label">Recent Transfers</div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Transfer Form -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-paper-plane me-2"></i>
                            New Crypto Transfer
                        </h5>
                    </div>
                        <div class="card-body">
                            <form method="POST" action="" id="cryptoTransferForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="crypto_type" class="form-label">Cryptocurrency Type</label>
                                            <select class="form-select" id="crypto_type" name="crypto_type" required>
                                                <option value="">Select Cryptocurrency</option>
                                                <option value="BTC">Bitcoin (BTC)</option>
                                                <option value="ETH">Ethereum (ETH)</option>
                                                <option value="LTC">Litecoin (LTC)</option>
                                                <option value="XRP">Ripple (XRP)</option>
                                                <option value="ADA">Cardano (ADA)</option>
                                                <option value="DOT">Polkadot (DOT)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="amount" name="amount" 
                                                       step="0.00000001" min="0.001" max="100" required>
                                                <span class="input-group-text" id="crypto-symbol">CRYPTO</span>
                                            </div>
                                            <div class="form-text">Minimum: 0.001, Maximum: 100 per transaction</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="recipient_address" class="form-label">Recipient Wallet Address</label>
                                    <input type="text" class="form-control" id="recipient_address" name="recipient_address" 
                                           placeholder="Enter the recipient's wallet address" required>
                                    <div class="form-text">Double-check the address. Crypto transfers cannot be reversed.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="transfer_note" class="form-label">Transfer Note (Optional)</label>
                                    <textarea class="form-control" id="transfer_note" name="transfer_note" rows="3" 
                                              placeholder="Add a note for your records (optional)"></textarea>
                                </div>



                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>Reset Form
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>Submit Transfer Request
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Recent Transfers Sidebar -->
                <div class="col-lg-4">
                    <div class="card crypto-card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Transfers
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_transfers)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-3"></i>
                                <p>No recent transfers</p>
                            </div>
                            <?php else: ?>
                            <div class="transfer-list">
                                <?php foreach ($recent_transfers as $transfer): ?>
                                <div class="transfer-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($transfer['amount']); ?> <?php echo htmlspecialchars($transfer['cryptocurrency'] ?? $transfer['crypto_type']); ?></div>
                                            <div class="text-muted small"><?php echo date('M j, Y', strtotime($transfer['created_at'])); ?></div>
                                        </div>
                                        <span class="badge bg-<?php echo $transfer['status'] === 'approved' ? 'success' : ($transfer['status'] === 'declined' ? 'danger' : 'warning'); ?>">
                                            <?php echo ucfirst($transfer['status']); ?>
                                        </span>
                                    </div>
                                    <div class="text-muted small mt-1">
                                        To: <?php echo substr($transfer['destination_address'] ?? $transfer['recipient_address'], 0, 10) . '...'; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="history.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<style>
.crypto-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 2rem;
    color: white;
    margin-bottom: 2rem;
}

.crypto-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.crypto-hero .hero-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.crypto-hero .hero-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.crypto-hero .hero-stats {
    font-size: 0.95rem;
    opacity: 0.8;
}

.crypto-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}
</style>

<script src="crypto.js"></script>
