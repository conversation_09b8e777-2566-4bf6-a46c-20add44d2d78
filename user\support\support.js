/**
 * Help & Support Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Support page loaded');
    initializeSupportPage();
});

/**
 * Initialize support page functionality
 */
function initializeSupportPage() {
    // Initialize form handlers
    initializeSupportForm();
    
    // Add hover effects to help items
    const helpItems = document.querySelectorAll('.help-item');
    helpItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
            this.style.boxShadow = '0 12px 35px rgba(0, 0, 0, 0.2)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        });
    });

    // Add hover effects to ticket items
    const ticketItems = document.querySelectorAll('.ticket-item');
    ticketItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(8px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
}

/**
 * Initialize support form
 */
function initializeSupportForm() {
    const form = document.getElementById('supportTicketForm');
    const categorySelect = document.getElementById('issueCategory');
    const descriptionTextarea = document.getElementById('description');
    const charCount = document.getElementById('charCount');
    
    // Handle category change
    categorySelect.addEventListener('change', function() {
        const transactionGroup = document.getElementById('transactionGroup');
        if (this.value === 'transaction_issue') {
            transactionGroup.style.display = 'block';
        } else {
            transactionGroup.style.display = 'none';
        }
    });
    
    // Handle character count
    descriptionTextarea.addEventListener('input', function() {
        const length = this.value.length;
        const maxLength = 1000;
        
        charCount.textContent = length;
        
        if (length > maxLength * 0.9) {
            charCount.parentElement.classList.add('warning');
        } else {
            charCount.parentElement.classList.remove('warning');
        }
        
        if (length > maxLength) {
            charCount.parentElement.classList.add('danger');
            this.value = this.value.substring(0, maxLength);
            charCount.textContent = maxLength;
        } else {
            charCount.parentElement.classList.remove('danger');
        }
    });
    
    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        handleTicketSubmission();
    });
}

/**
 * Handle ticket form submission
 */
async function handleTicketSubmission() {
    const form = document.getElementById('supportTicketForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    // Validate form
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    try {
        // Show loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
        
        // Prepare form data
        const formData = new FormData(form);
        
        // Add user information
        formData.append('user_id', window.supportData.userId);
        
        // Simulate API call (replace with actual endpoint)
        const response = await fetch('submit-ticket.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Show success modal
            showSuccessModal(result.ticket_number);
            
            // Reset form
            resetTicketForm();
            
        } else {
            throw new Error(result.message || 'Failed to submit ticket');
        }
        
    } catch (error) {
        console.error('Ticket submission error:', error);
        showNotification(error.message || 'Failed to submit ticket. Please try again.', 'error');
    } finally {
        // Reset button state
        submitBtn.classList.remove('loading');
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
}

/**
 * Show success modal
 */
function showSuccessModal(ticketNumber) {
    const successContent = document.getElementById('successContent');
    successContent.innerHTML = `
        <div class="success-message">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h4>Ticket Submitted Successfully!</h4>
            <p>Your support ticket has been created with the following details:</p>
            <div class="ticket-details">
                <div class="detail-item">
                    <strong>Ticket Number:</strong> #${ticketNumber}
                </div>
                <div class="detail-item">
                    <strong>Status:</strong> Open
                </div>
                <div class="detail-item">
                    <strong>Expected Response:</strong> Within 24 hours
                </div>
            </div>
            <p class="note">You will receive email updates about your ticket status. You can also check the status in your support dashboard.</p>
        </div>
    `;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('successModal'));
    modal.show();
}

/**
 * Reset ticket form
 */
function resetTicketForm() {
    const form = document.getElementById('supportTicketForm');
    form.reset();
    
    // Hide transaction group
    document.getElementById('transactionGroup').style.display = 'none';
    
    // Reset character count
    document.getElementById('charCount').textContent = '0';
    document.querySelector('.character-count').classList.remove('warning', 'danger');
    
    // Remove validation classes
    const inputs = form.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.classList.remove('is-invalid');
    });
}

/**
 * Scroll to ticket form
 */
function scrollToTicketForm() {
    document.getElementById('ticketFormSection').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

/**
 * Show contact information
 */
function showContactInfo() {
    const contactInfo = document.getElementById('contactInfo');
    if (contactInfo.style.display === 'none' || !contactInfo.style.display) {
        contactInfo.style.display = 'block';
        contactInfo.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    } else {
        contactInfo.style.display = 'none';
    }
}

/**
 * Show live chat (placeholder)
 */
function showLiveChat() {
    showNotification('Live chat feature coming soon! Please submit a ticket for immediate assistance.', 'info');
}

/**
 * View FAQ (placeholder)
 */
function viewFAQ() {
    showNotification('FAQ section coming soon! Please submit a ticket for any questions.', 'info');
}

/**
 * View specific ticket
 */
function viewTicket(ticketId) {
    window.location.href = `ticket-details.php?id=${ticketId}`;
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 350px;
    `;
    
    // Set background color based on type
    switch(type) {
        case 'success':
            notification.style.background = '#28a745';
            break;
        case 'error':
            notification.style.background = '#dc3545';
            break;
        default:
            notification.style.background = '#17a2b8';
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 4000);
}

/**
 * Add custom styles for success modal
 */
function addSuccessModalStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .success-message {
            text-align: center;
            padding: 1rem;
        }
        
        .success-icon {
            font-size: 3rem;
            color: #28a745;
            margin-bottom: 1rem;
        }
        
        .success-message h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .ticket-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: left;
        }
        
        .detail-item {
            margin-bottom: 0.5rem;
        }
        
        .detail-item:last-child {
            margin-bottom: 0;
        }
        
        .note {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 1rem;
        }
    `;
    document.head.appendChild(style);
}

// Add success modal styles when page loads
addSuccessModalStyles();
