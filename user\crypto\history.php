<?php
/**
 * Crypto Transfer History
 * Display user's cryptocurrency transfer history
 */

// Set page variables
$page_title = 'Crypto Transfer History';
$current_page = 'crypto';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Check if crypto transfers feature is enabled
$crypto_enabled_query = "SELECT setting_value FROM admin_settings WHERE setting_key = 'crypto_transfers_enabled'";
$crypto_enabled_result = $db->query($crypto_enabled_query);
$crypto_enabled = $crypto_enabled_result->fetch_assoc()['setting_value'] ?? '0';

if ($crypto_enabled !== '1') {
    header('Location: ../dashboard/index.php');
    exit();
}

// Pagination settings
$records_per_page = 10;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $records_per_page;

// Filter settings
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$crypto_filter = isset($_GET['crypto']) ? $_GET['crypto'] : '';

// Build WHERE clause
$where_conditions = ["account_id = ?"];
$params = [$user_id];

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if (!empty($crypto_filter)) {
    $where_conditions[] = "crypto_type = ?";
    $params[] = $crypto_filter;
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM crypto_transfers $where_clause";
$count_result = $db->query($count_query, $params);
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $records_per_page);

// Get transfers
$transfers_query = "SELECT * FROM crypto_transfers 
                   $where_clause 
                   ORDER BY created_at DESC 
                   LIMIT $records_per_page OFFSET $offset";
$transfers_result = $db->query($transfers_query, $params);
$transfers = [];
while ($row = $transfers_result->fetch_assoc()) {
    $transfers[] = $row;
}

// Get statistics
$stats_query = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                SUM(CASE WHEN status = 'declined' THEN 1 ELSE 0 END) as declined
                FROM crypto_transfers WHERE account_id = ?";
$stats_result = $db->query($stats_query, [$user_id]);
$stats = $stats_result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Online Banking</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="crypto.css" rel="stylesheet">
    
    <!-- Dynamic CSS -->
    <?php echo $dynamic_css; ?>
</head>
<body>
    <!-- Include Sidebar -->
    <?php include '../shared/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="crypto-header">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="crypto-title">
                            <i class="fas fa-history me-3"></i>
                            Transfer History
                        </h1>
                        <p class="crypto-subtitle">View your cryptocurrency transfer history</p>
                    </div>
                    <div class="col-auto">
                        <a href="transfer.php" class="btn btn-outline-light">
                            <i class="fas fa-plus me-2"></i>New Transfer
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card crypto-card text-center">
                        <div class="card-body">
                            <div class="display-6 fw-bold text-primary"><?php echo $stats['total']; ?></div>
                            <div class="text-muted">Total Transfers</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card crypto-card text-center">
                        <div class="card-body">
                            <div class="display-6 fw-bold text-warning"><?php echo $stats['pending']; ?></div>
                            <div class="text-muted">Pending</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card crypto-card text-center">
                        <div class="card-body">
                            <div class="display-6 fw-bold text-success"><?php echo $stats['approved']; ?></div>
                            <div class="text-muted">Approved</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card crypto-card text-center">
                        <div class="card-body">
                            <div class="display-6 fw-bold text-danger"><?php echo $stats['declined']; ?></div>
                            <div class="text-muted">Declined</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="container-fluid mt-4">
            <div class="card crypto-card">
                <div class="card-body">
                    <form method="GET" action="" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                <option value="declined" <?php echo $status_filter === 'declined' ? 'selected' : ''; ?>>Declined</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="crypto" class="form-label">Cryptocurrency</label>
                            <select class="form-select" id="crypto" name="crypto">
                                <option value="">All Cryptocurrencies</option>
                                <option value="BTC" <?php echo $crypto_filter === 'BTC' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                                <option value="ETH" <?php echo $crypto_filter === 'ETH' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                                <option value="LTC" <?php echo $crypto_filter === 'LTC' ? 'selected' : ''; ?>>Litecoin (LTC)</option>
                                <option value="XRP" <?php echo $crypto_filter === 'XRP' ? 'selected' : ''; ?>>Ripple (XRP)</option>
                                <option value="ADA" <?php echo $crypto_filter === 'ADA' ? 'selected' : ''; ?>>Cardano (ADA)</option>
                                <option value="DOT" <?php echo $crypto_filter === 'DOT' ? 'selected' : ''; ?>>Polkadot (DOT)</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-2"></i>Filter
                            </button>
                            <a href="history.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Transfer History Table -->
        <div class="container-fluid mt-4">
            <div class="card crypto-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Transfer History
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($transfers)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No transfers found</h5>
                        <p class="text-muted">You haven't made any cryptocurrency transfers yet.</p>
                        <a href="transfer.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Make Your First Transfer
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Cryptocurrency</th>
                                    <th>Amount</th>
                                    <th>Recipient</th>
                                    <th>Transaction Hash</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transfers as $transfer): ?>
                                <tr>
                                    <td>
                                        <div class="fw-bold"><?php echo date('M j, Y', strtotime($transfer['created_at'])); ?></div>
                                        <div class="text-muted small"><?php echo date('g:i A', strtotime($transfer['created_at'])); ?></div>
                                    </td>
                                    <td>
                                        <span class="crypto-icon <?php echo strtolower($transfer['crypto_type']); ?>">
                                            <?php echo substr($transfer['crypto_type'], 0, 1); ?>
                                        </span>
                                        <?php echo htmlspecialchars($transfer['crypto_type']); ?>
                                    </td>
                                    <td class="fw-bold">
                                        <?php echo number_format($transfer['amount'], 8); ?>
                                        <small class="text-muted"><?php echo htmlspecialchars($transfer['crypto_type']); ?></small>
                                    </td>
                                    <td>
                                        <code class="small"><?php echo substr($transfer['recipient_address'], 0, 10) . '...'; ?></code>
                                    </td>
                                    <td>
                                        <code class="small"><?php echo substr($transfer['transaction_hash'], 0, 12) . '...'; ?></code>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $transfer['status'] === 'approved' ? 'success' : ($transfer['status'] === 'declined' ? 'danger' : 'warning'); ?>">
                                            <?php echo ucfirst($transfer['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewTransfer(<?php echo $transfer['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="Transfer history pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&crypto=<?php echo $crypto_filter; ?>">Previous</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&crypto=<?php echo $crypto_filter; ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&crypto=<?php echo $crypto_filter; ?>">Next</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Details Modal -->
    <div class="modal fade" id="transferModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Transfer Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="transferModalBody">
                    <!-- Transfer details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="crypto.js"></script>
    <script>
        function viewTransfer(transferId) {
            // This would typically load transfer details via AJAX
            // For now, we'll show a placeholder
            document.getElementById('transferModalBody').innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading transfer details...</p>
                </div>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('transferModal'));
            modal.show();
            
            // Simulate loading delay
            setTimeout(() => {
                document.getElementById('transferModalBody').innerHTML = `
                    <p>Transfer details for ID: ${transferId}</p>
                    <p>This feature will be implemented in a future update.</p>
                `;
            }, 1000);
        }
    </script>
</body>
</html>
