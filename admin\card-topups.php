<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Card Top-ups Management';

// Handle top-up approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        $topup_id = intval($_POST['topup_id']);
        $action = $_POST['action'];
        $admin_notes = trim($_POST['admin_notes'] ?? '');
        
        if (!in_array($action, ['approve', 'decline'])) {
            throw new Exception("Invalid action.");
        }
        
        // Get top-up details
        $topup_query = "SELECT ct.*, a.first_name, a.last_name, a.email, 
                       vc.card_number, vc.card_name, pm.method_name
                       FROM card_topups ct 
                       LEFT JOIN accounts a ON ct.account_id = a.id 
                       LEFT JOIN virtual_cards vc ON ct.card_id = vc.id
                       LEFT JOIN payment_methods pm ON ct.payment_method_id = pm.id
                       WHERE ct.id = ? AND ct.status = 'pending'";
        $topup_result = $db->query($topup_query, [$topup_id]);
        $topup = $topup_result->fetch_assoc();
        
        if (!$topup) {
            throw new Exception("Top-up not found or already processed.");
        }
        
        $new_status = $action === 'approve' ? 'approved' : 'declined';
        
        // Start transaction
        $db->beginTransaction();
        
        try {
            // Update top-up status
            $update_query = "UPDATE card_topups SET 
                            status = ?, admin_notes = ?, reviewed_by = ?, reviewed_at = NOW() 
                            WHERE id = ?";
            $db->query($update_query, [$new_status, $admin_notes, $_SESSION['user_id'], $topup_id]);
            
            // If approved, update card balance
            if ($action === 'approve') {
                $balance_update_query = "UPDATE virtual_cards SET balance = balance + ? WHERE id = ?";
                $db->query($balance_update_query, [$topup['amount'], $topup['card_id']]);
            }
            
            // Send email notification (placeholder for now)
            $email_subject = $action === 'approve' ? 
                'Card Top-up Approved' : 
                'Card Top-up Update';
                
            $email_body = $action === 'approve' ?
                "Dear {$topup['first_name']},\n\nYour card top-up of $" . number_format($topup['amount'], 2) . " has been approved and added to your card ending in " . substr($topup['card_number'], -4) . ".\n\nReference: {$topup['transaction_reference']}\n\nThank you for using our services." :
                "Dear {$topup['first_name']},\n\nWe have reviewed your card top-up request. Unfortunately, we are unable to process your top-up at this time.\n\nReason: {$admin_notes}\n\nIf you have any questions, please contact our support team.";
            
            // Log email notification
            $email_insert = "INSERT INTO email_notifications (
                recipient_id, email_address, subject, message_body, notification_type, 
                related_record_id, related_record_type, status
            ) VALUES (?, ?, ?, ?, ?, ?, 'card_topup', 'pending')";
            
            $db->query($email_insert, [
                $topup['account_id'],
                $topup['email'],
                $email_subject,
                $email_body,
                $action === 'approve' ? 'topup_approval' : 'topup_decline',
                $topup_id
            ]);
            
            $db->commit();
            $success = "Top-up " . ($action === 'approve' ? 'approved' : 'declined') . " successfully!";
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Pagination and filtering
$records_per_page = 20;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $records_per_page;

$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$method_filter = isset($_GET['method']) ? $_GET['method'] : '';

// Build WHERE clause
$where_conditions = [];
$params = [];

if (!empty($status_filter)) {
    $where_conditions[] = "ct.status = ?";
    $params[] = $status_filter;
}

if (!empty($method_filter)) {
    $where_conditions[] = "ct.payment_method_id = ?";
    $params[] = $method_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM card_topups ct $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get top-ups
    $topups_query = "SELECT ct.*, 
                    a.first_name, a.last_name, a.username, a.account_number,
                    vc.card_number, vc.card_name,
                    pm.method_name,
                    admin.first_name as admin_first_name, admin.last_name as admin_last_name
                    FROM card_topups ct 
                    LEFT JOIN accounts a ON ct.account_id = a.id 
                    LEFT JOIN virtual_cards vc ON ct.card_id = vc.id
                    LEFT JOIN payment_methods pm ON ct.payment_method_id = pm.id
                    LEFT JOIN accounts admin ON ct.reviewed_by = admin.id 
                    $where_clause
                    ORDER BY ct.created_at DESC 
                    LIMIT $records_per_page OFFSET $offset";
    
    $topups_result = $db->query($topups_query, $params);
    $topups = [];
    while ($row = $topups_result->fetch_assoc()) {
        $topups[] = $row;
    }
    
    // Get statistics
    $stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                    SUM(CASE WHEN status = 'declined' THEN 1 ELSE 0 END) as declined,
                    SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as total_approved_amount
                    FROM card_topups";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch_assoc();
    
    // Get payment methods for filter
    $payment_methods_query = "SELECT * FROM payment_methods ORDER BY method_name";
    $payment_methods_result = $db->query($payment_methods_query);
    $payment_methods = [];
    while ($row = $payment_methods_result->fetch_assoc()) {
        $payment_methods[] = $row;
    }
    
} catch (Exception $e) {
    $error = "Failed to load top-ups: " . $e->getMessage();
    $topups = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'pending' => 0, 'approved' => 0, 'declined' => 0, 'total_approved_amount' => 0];
    $payment_methods = [];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Card Top-ups</li>
    </ol>
</nav>

<!-- Success/Error Messages -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-credit-card"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total']); ?></div>
                        <div class="text-muted">Total Top-ups</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending']); ?></div>
                        <div class="text-muted">Pending Review</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['approved']); ?></div>
                        <div class="text-muted">Approved</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">$<?php echo number_format($stats['total_approved_amount'], 2); ?></div>
                        <div class="text-muted">Total Approved</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">Filter Top-ups</h3>
    </div>
    <div class="card-body">
        <form method="GET" action="" class="row g-3">
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                    <option value="declined" <?php echo $status_filter === 'declined' ? 'selected' : ''; ?>>Declined</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="method" class="form-label">Payment Method</label>
                <select class="form-select" id="method" name="method">
                    <option value="">All Methods</option>
                    <?php foreach ($payment_methods as $method): ?>
                    <option value="<?php echo $method['id']; ?>" <?php echo $method_filter == $method['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($method['method_name']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-2"></i>Filter
                </button>
                <a href="card-topups.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Top-ups Table -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-credit-card me-2"></i>
            Card Top-up Requests
        </h3>
        <div class="card-subtitle">
            Manage and review virtual card top-up requests
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($topups)): ?>
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No top-ups found</h5>
            <p class="text-muted">No card top-up requests match your current filters.</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-vcenter">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Card</th>
                        <th>Amount</th>
                        <th>Payment Method</th>
                        <th>Reference</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($topups as $topup): ?>
                    <tr>
                        <td>
                            <div class="d-flex py-1 align-items-center">
                                <div class="flex-fill">
                                    <div class="font-weight-medium"><?php echo htmlspecialchars($topup['first_name'] . ' ' . $topup['last_name']); ?></div>
                                    <div class="text-muted"><?php echo htmlspecialchars($topup['username']); ?></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="font-weight-medium"><?php echo htmlspecialchars($topup['card_name']); ?></div>
                            <div class="text-muted small">**** <?php echo substr($topup['card_number'], -4); ?></div>
                        </td>
                        <td class="text-muted">
                            <div class="font-weight-medium">$<?php echo number_format($topup['amount'], 2); ?></div>
                        </td>
                        <td>
                            <span class="badge bg-secondary"><?php echo htmlspecialchars($topup['method_name']); ?></span>
                        </td>
                        <td>
                            <code class="small"><?php echo htmlspecialchars($topup['transaction_reference']); ?></code>
                        </td>
                        <td class="text-muted">
                            <?php echo date('M j, Y', strtotime($topup['created_at'])); ?>
                            <div class="small"><?php echo date('g:i A', strtotime($topup['created_at'])); ?></div>
                        </td>
                        <td>
                            <span class="badge bg-<?php echo $topup['status'] === 'approved' ? 'success' : ($topup['status'] === 'declined' ? 'danger' : 'warning'); ?>">
                                <?php echo ucfirst($topup['status']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-list flex-nowrap">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewTopup(<?php echo $topup['id']; ?>)">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if ($topup['status'] === 'pending'): ?>
                                <button class="btn btn-sm btn-outline-success" onclick="reviewTopup(<?php echo $topup['id']; ?>, 'approve')">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="reviewTopup(<?php echo $topup['id']; ?>, 'decline')">
                                    <i class="fas fa-times"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Top-up Details Modal -->
<div class="modal fade" id="topupModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Top-up Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="topupModalBody">
                <!-- Top-up details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Review Top-up Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewModalTitle">Review Top-up</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="topup_id" id="reviewTopupId">
                    <input type="hidden" name="action" id="reviewAction">

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                  placeholder="Add notes about this decision (optional for approval, required for decline)"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="reviewActionText">This action will update the top-up status and notify the user via email.</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="reviewSubmitBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewTopup(topupId) {
    // Load top-up details via AJAX (placeholder for now)
    document.getElementById('topupModalBody').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading top-up details...</p>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('topupModal'));
    modal.show();

    // Simulate loading delay
    setTimeout(() => {
        document.getElementById('topupModalBody').innerHTML = `
            <p>Top-up details for ID: ${topupId}</p>
            <p>This feature will be implemented in a future update.</p>
        `;
    }, 1000);
}

function reviewTopup(topupId, action) {
    document.getElementById('reviewTopupId').value = topupId;
    document.getElementById('reviewAction').value = action;

    const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
    const title = document.getElementById('reviewModalTitle');
    const submitBtn = document.getElementById('reviewSubmitBtn');
    const actionText = document.getElementById('reviewActionText');
    const notesField = document.getElementById('admin_notes');

    if (action === 'approve') {
        title.textContent = 'Approve Top-up';
        submitBtn.textContent = 'Approve Top-up';
        submitBtn.className = 'btn btn-success';
        actionText.textContent = 'This will approve the top-up and add the funds to the user\'s virtual card.';
        notesField.placeholder = 'Add approval notes (optional)';
        notesField.required = false;
    } else {
        title.textContent = 'Decline Top-up';
        submitBtn.textContent = 'Decline Top-up';
        submitBtn.className = 'btn btn-danger';
        actionText.textContent = 'This will decline the top-up and notify the user. Please provide a reason for the decline.';
        notesField.placeholder = 'Reason for decline (required)';
        notesField.required = true;
    }

    modal.show();
}

// Auto-dismiss alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
});
</script>

<?php include 'includes/admin-footer.php'; ?>
