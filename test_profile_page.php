<?php
/**
 * Test Profile Page for Syntax Errors
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Testing Profile Page ===\n\n";

// Test if the profile page can be included without errors
echo "1. Testing profile page syntax:\n";

try {
    // Start output buffering to catch any output
    ob_start();
    
    // Mock session data
    session_start();
    $_SESSION['user_id'] = 5; // jamesbong101
    
    // Include the profile page
    include 'user/profile/index.php';
    
    // Get the output
    $output = ob_get_clean();
    
    echo "✅ Profile page loaded without syntax errors\n";
    echo "✅ Output length: " . strlen($output) . " characters\n";
    
    // Check if essential elements are present
    if (strpos($output, 'Profile & Settings') !== false) {
        echo "✅ Page title found\n";
    } else {
        echo "❌ Page title missing\n";
    }
    
    if (strpos($output, 'profile-hero') !== false) {
        echo "✅ Hero section found\n";
    } else {
        echo "❌ Hero section missing\n";
    }
    
    if (strpos($output, 'profile-container') !== false) {
        echo "✅ Profile container found\n";
    } else {
        echo "❌ Profile container missing\n";
    }
    
} catch (ParseError $e) {
    echo "❌ Parse Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
}

echo "\n2. Testing required files:\n";

$required_files = [
    'user/profile/index.php',
    'user/profile/profile.css',
    'user/profile/profile.js',
    'user/shared/header.php',
    'user/shared/sidebar.php',
    'user/shared/footer.php',
    'config/config.php',
    'config/dynamic-css.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists\n";
    } else {
        echo "❌ $file missing\n";
    }
}

echo "\n3. Testing database connection:\n";

try {
    require_once 'config/database.php';
    $db = getDB();
    
    // Test user query
    $user_query = "SELECT id, username, first_name, last_name, email FROM accounts WHERE id = 5";
    $result = $db->query($user_query);
    
    if ($result && $user = $result->fetch_assoc()) {
        echo "✅ Database connection working\n";
        echo "✅ Test user found: {$user['username']} ({$user['first_name']} {$user['last_name']})\n";
    } else {
        echo "❌ Test user not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n=== Profile Page Test Complete ===\n";
?>
