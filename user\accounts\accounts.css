@import url('../dashboard/dashboard.css');
/* Internal Transfers Page Styles */

/* Accounts Hero Section */
.accounts-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 12px;
    padding: 2rem;
    color: white;
    margin-bottom: 1.5rem;
}

.accounts-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.accounts-hero .hero-main {
    flex: 1;
}

.accounts-hero .hero-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.accounts-hero .hero-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.accounts-hero .hero-stats {
    font-size: 0.9rem;
    opacity: 0.8;
}

.accounts-hero .hero-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.accounts-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
}

/* Stats Cards Section */
.balance-overview-new {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.balance-card-new {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.balance-card-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.balance-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.balance-icon.card {
    background: var(--primary-color);
}

.balance-info {
    flex: 1;
}

.balance-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.balance-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.balance-amount.available {
    color: var(--primary-color);
}

.balance-amount.card {
    color: #f59e0b;
}

.balance-subtitle {
    font-size: 0.75rem;
    color: #9ca3af;
    font-weight: 500;
}

/* Balances Overview */
.balances-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.balance-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.balance-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* Removed top border from balance cards */

.main-account::before {
    background: linear-gradient(90deg, #1e3c72, #2a5298);
}

.virtual-card::before {
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.account-info h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.account-number {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.account-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.main-account .account-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.virtual-card .account-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.balance-amount {
    margin-bottom: 1.5rem;
}

.balance-amount .currency {
    font-size: 1rem;
    color: #6c757d;
    font-weight: 500;
    margin-right: 0.5rem;
}

.balance-amount .amount {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
}

.balance-actions {
    display: flex;
    gap: 0.5rem;
}

/* Transfer Section */
.transfer-section {
    margin-bottom: 3rem;
}

.transfer-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.transfer-card .card-header {
    margin-bottom: 2rem;
    text-align: center;
}

.transfer-card .card-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.transfer-card .card-header p {
    margin: 0;
    color: #6c757d;
}

/* Transfer Form - Full Width */
.transfer-form {
    max-width: 100%;
    margin: 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 1rem;
    align-items: end;
    margin-bottom: 2rem;
}

.transfer-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(32, 107, 196, 0.1);
}

.amount-input {
    position: relative;
    margin: 1rem 0;
}

.currency-symbol {
    position: absolute;
    left: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-weight: 700;
    color: var(--primary-color);
    z-index: 2;
    font-size: 1.2rem;
}

.amount-input .form-control {
    padding-left: 3rem;
    padding-right: 1.5rem;
    height: 60px;
    font-size: 1.25rem;
    font-weight: 600;
    border: 3px solid #e9ecef;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.amount-input .form-control:focus {
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 4px rgba(32, 107, 196, 0.1);
    transform: translateY(-2px);
}

/* Ensure transfer amount input is always visible */
#transferAmount {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
}

/* Transfer Confirmation Modal Styling */
.transfer-confirm-modal {
    border-radius: 16px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.transfer-confirm-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-bottom: none;
    padding: 1.5rem;
}

.transfer-confirm-header .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
}

.transfer-confirm-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.transfer-confirm-header .btn-close:hover {
    opacity: 1;
}

.transfer-confirm-body {
    padding: 2rem;
    background: #f8f9fa;
}

.transfer-summary-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.transfer-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.transfer-detail-row:last-child {
    border-bottom: none;
    margin-top: 0.5rem;
    padding-top: 1rem;
    border-top: 2px solid #e5e7eb;
}

.transfer-detail-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.transfer-detail-value {
    font-weight: 600;
    color: #1f2937;
    text-align: right;
}

.transfer-amount-highlight {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.transfer-fee-free {
    color: #059669;
    font-weight: 700;
}

.transfer-warning {
    background: #fef3c7;
    border: 1px solid #fbbf24;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.transfer-warning i {
    color: #d97706;
    font-size: 1.1rem;
}

.transfer-warning-text {
    color: #92400e;
    font-size: 0.875rem;
    font-weight: 500;
}

.transfer-confirm-footer {
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.transfer-confirm-footer .btn {
    min-width: 120px;
    font-weight: 600;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
}

/* Amount Suggestion Buttons */
.amount-suggestions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.amount-btn {
    background: rgba(34, 197, 94, 0.1);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.amount-btn:hover {
    background: rgba(34, 197, 94, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.amount-btn:active {
    transform: translateY(0);
}

/* Responsive Design for Transfer Modal */
@media (max-width: 768px) {
    .transfer-confirm-body {
        padding: 1.5rem;
    }

    .transfer-summary-card {
        padding: 1rem;
    }

    .transfer-detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .transfer-detail-value {
        text-align: left;
    }

    .transfer-confirm-footer {
        flex-direction: column;
        gap: 0.75rem;
    }

    .transfer-confirm-footer .btn {
        width: 100%;
        min-width: auto;
    }

    .amount-suggestions {
        gap: 0.25rem;
    }

    .amount-btn {
        min-width: 70px;
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

.amount-suggestions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.amount-btn {
    padding: 0.375rem 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: white;
    color: #6c757d;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.amount-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(32, 107, 196, 0.05);
}

/* Transfer Summary */
.transfer-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}

.transfer-summary h4 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-row.total {
    font-weight: 600;
    color: var(--primary-color);
    border-top: 2px solid #e9ecef;
    margin-top: 0.5rem;
    padding-top: 1rem;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Recent Transfers */
.recent-transfers {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
}

.view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
}

.view-all-link:hover {
    text-decoration: underline;
}

.transfers-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.transfer-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.transfer-item:hover {
    background: #f8f9fa;
    border-color: var(--primary-color);
}

.transfer-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.transfer-details {
    flex: 1;
}

.transfer-description {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.transfer-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.transfer-amount {
    font-weight: 600;
    font-size: 1.1rem;
}

.transfer-amount.credit {
    color: #28a745;
}

.transfer-amount.debit {
    color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .accounts-container {
        padding: 1rem;
    }
    
    .balances-overview {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .transfer-arrow {
        transform: rotate(90deg);
        margin: 0 auto;
    }
    
    .amount-suggestions {
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .transfer-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .balance-card {
        padding: 1.5rem;
    }
    
    .balance-amount .amount {
        font-size: 1.5rem;
    }
    
    .transfer-card {
        padding: 1.5rem;
    }
    
    .recent-transfers {
        padding: 1.5rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
