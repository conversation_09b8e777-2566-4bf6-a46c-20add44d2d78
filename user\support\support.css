/* Help & Support Page Styles */

/* Support Hero Section */
.support-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 12px;
    padding: 2rem;
    color: white;
    margin-bottom: 1.5rem;
}

.support-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.support-hero .hero-main {
    flex: 1;
}

.support-hero .hero-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.support-hero .hero-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.support-hero .hero-stats {
    font-size: 0.9rem;
    opacity: 0.8;
}

.support-hero .hero-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.support-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
}

/* Quick Help Section */
.quick-help {
    margin-bottom: 3rem;
}

.quick-help h3 {
    margin: 0 0 1.5rem 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.help-item {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.help-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.help-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.help-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.help-content p {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.4;
}

/* Ticket Form Section */
.ticket-form-section {
    margin-bottom: 3rem;
}

.form-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.form-card .card-header {
    margin-bottom: 2rem;
    text-align: center;
}

.form-card .card-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.form-card .card-header p {
    margin: 0;
    color: #6c757d;
}

/* Support Form */
.support-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(32, 107, 196, 0.1);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

.character-count {
    text-align: right;
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.character-count.warning {
    color: #ffc107;
}

.character-count.danger {
    color: #dc3545;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Previous Tickets */
.previous-tickets {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
}

.view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
}

.view-all-link:hover {
    text-decoration: underline;
}

.tickets-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ticket-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
}

.ticket-item:hover {
    background: #f8f9fa;
    border-color: var(--primary-color);
    transform: translateX(4px);
}

.ticket-info {
    flex: 1;
}

.ticket-subject {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.ticket-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.ticket-id {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.ticket-status {
    margin-left: 1rem;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-open {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-in_progress {
    background: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
}

.status-waiting_response {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-resolved {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-closed {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* Contact Information */
.contact-info {
    margin-bottom: 2rem;
}

.info-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.info-card .card-header {
    margin-bottom: 2rem;
    text-align: center;
}

.info-card .card-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.contact-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.contact-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.contact-content p {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    color: #2c3e50;
    font-weight: 500;
}

.contact-content small {
    color: #6c757d;
    font-size: 0.85rem;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .support-container {
        padding: 1rem;
    }
    
    .help-grid {
        grid-template-columns: 1fr;
    }
    
    .help-item {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .contact-details {
        grid-template-columns: 1fr;
    }
    
    .ticket-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .ticket-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .form-card,
    .info-card,
    .previous-tickets {
        padding: 1.5rem;
    }
    
    .help-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .contact-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}
