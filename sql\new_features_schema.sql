-- New Features Database Schema
-- Online Banking Application - IRS, Crypto Transfers, Deposits, Card Top-ups
-- Created: 2025-08-03

-- =====================================================
-- 1. ADMIN SETTINGS TABLE (for feature toggles)
-- =====================================================

DROP TABLE IF EXISTS `admin_settings`;
CREATE TABLE `admin_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text COLLATE utf8mb4_unicode_ci,
  `setting_type` enum('boolean','string','integer','json') COLLATE utf8mb4_unicode_ci DEFAULT 'string',
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings
INSERT INTO `admin_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('irs_feature_enabled', '1', 'boolean', 'Enable/disable IRS tax return assistance feature'),
('crypto_transfers_enabled', '1', 'boolean', 'Enable/disable crypto transfer feature'),
('crypto_deposits_enabled', '1', 'boolean', 'Enable/disable crypto deposit feature'),
('card_topups_enabled', '1', 'boolean', 'Enable/disable virtual card top-up feature');

-- =====================================================
-- 2. IRS TAX RETURN APPLICATIONS
-- =====================================================

DROP TABLE IF EXISTS `irs_applications`;
CREATE TABLE `irs_applications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL,
  `application_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  
  -- Personal Information
  `first_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `middle_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ssn` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_of_birth` date NOT NULL,
  `phone_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  
  -- Address Information
  `street_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `city` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `zip_code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT 'United States',
  
  -- Tax Information
  `tax_year` year NOT NULL,
  `filing_status` enum('single','married_filing_jointly','married_filing_separately','head_of_household','qualifying_widow') COLLATE utf8mb4_unicode_ci NOT NULL,
  `annual_income` decimal(15,2) NOT NULL,
  `employment_type` enum('employed','self_employed','unemployed','retired','student') COLLATE utf8mb4_unicode_ci NOT NULL,
  `employer_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `employer_ein` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  
  -- Dependents
  `number_of_dependents` int(11) DEFAULT 0,
  `dependents_info` json DEFAULT NULL,
  
  -- Financial Information
  `bank_account_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `routing_number` varchar(9) COLLATE utf8mb4_unicode_ci NOT NULL,
  `previous_year_agi` decimal(15,2) DEFAULT NULL,
  `estimated_refund` decimal(15,2) DEFAULT NULL,
  
  -- Additional Information
  `has_foreign_income` boolean DEFAULT FALSE,
  `has_business_income` boolean DEFAULT FALSE,
  `has_rental_income` boolean DEFAULT FALSE,
  `has_investment_income` boolean DEFAULT FALSE,
  `special_circumstances` text COLLATE utf8mb4_unicode_ci,
  
  -- Application Status
  `status` enum('pending','under_review','approved','declined','completed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `admin_notes` text COLLATE utf8mb4_unicode_ci,
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `application_number` (`application_number`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_status` (`status`),
  KEY `idx_tax_year` (`tax_year`),
  KEY `reviewed_by` (`reviewed_by`),
  CONSTRAINT `irs_applications_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `irs_applications_ibfk_2` FOREIGN KEY (`reviewed_by`) REFERENCES `accounts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. CRYPTO TRANSFERS (Simulation)
-- =====================================================

DROP TABLE IF EXISTS `crypto_transfers`;
CREATE TABLE `crypto_transfers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL,
  `transfer_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `source_wallet_id` int(11) NOT NULL,
  `destination_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `cryptocurrency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(18,8) NOT NULL,
  `usd_equivalent` decimal(15,2) NOT NULL,
  `network_fee` decimal(18,8) DEFAULT 0,
  `exchange_rate` decimal(15,8) NOT NULL,
  `transaction_hash` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('pending','processing','completed','failed','cancelled') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `receipt_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `transfer_number` (`transfer_number`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_source_wallet` (`source_wallet_id`),
  KEY `idx_status` (`status`),
  KEY `idx_cryptocurrency` (`cryptocurrency`),
  CONSTRAINT `crypto_transfers_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `crypto_transfers_ibfk_2` FOREIGN KEY (`source_wallet_id`) REFERENCES `crypto_wallets` (`wallet_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. CRYPTO DEPOSITS
-- =====================================================

DROP TABLE IF EXISTS `crypto_deposits`;
CREATE TABLE `crypto_deposits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL,
  `deposit_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `wallet_id` int(11) NOT NULL,
  `cryptocurrency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `deposit_amount` decimal(18,8) NOT NULL,
  `usd_equivalent` decimal(15,2) NOT NULL,
  `exchange_rate` decimal(15,8) NOT NULL,
  `admin_wallet_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_transaction_hash` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receipt_file_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('pending','under_review','approved','declined','credited') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `admin_notes` text COLLATE utf8mb4_unicode_ci,
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `credited_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `deposit_number` (`deposit_number`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_wallet_id` (`wallet_id`),
  KEY `idx_status` (`status`),
  KEY `idx_cryptocurrency` (`cryptocurrency`),
  KEY `reviewed_by` (`reviewed_by`),
  CONSTRAINT `crypto_deposits_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `crypto_deposits_ibfk_2` FOREIGN KEY (`wallet_id`) REFERENCES `crypto_wallets` (`wallet_id`) ON DELETE CASCADE,
  CONSTRAINT `crypto_deposits_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `accounts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. PAYMENT METHODS (Admin Configurable)
-- =====================================================

DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `method_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `method_type` enum('bank_account','crypto_wallet','payment_processor','other') COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_name` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `account_details` json NOT NULL,
  `processing_fee` decimal(5,2) DEFAULT 0.00,
  `minimum_amount` decimal(15,2) DEFAULT 0.00,
  `maximum_amount` decimal(15,2) DEFAULT 999999.99,
  `is_active` boolean DEFAULT TRUE,
  `sort_order` int(11) DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  KEY `idx_method_type` (`method_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `payment_methods_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 6. VIRTUAL CARD TOP-UPS
-- =====================================================

DROP TABLE IF EXISTS `card_topups`;
CREATE TABLE `card_topups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL,
  `topup_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `card_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `topup_amount` decimal(15,2) NOT NULL,
  `processing_fee` decimal(15,2) DEFAULT 0.00,
  `total_amount` decimal(15,2) NOT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'USD',
  `payment_reference` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_notes` text COLLATE utf8mb4_unicode_ci,
  `status` enum('pending','under_review','approved','declined','processed','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `admin_notes` text COLLATE utf8mb4_unicode_ci,
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY (`id`),
  UNIQUE KEY `topup_number` (`topup_number`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_payment_method` (`payment_method_id`),
  KEY `idx_status` (`status`),
  KEY `reviewed_by` (`reviewed_by`),
  CONSTRAINT `card_topups_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `card_topups_ibfk_2` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`) ON DELETE CASCADE,
  CONSTRAINT `card_topups_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `accounts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 7. EMAIL NOTIFICATIONS LOG
-- =====================================================

DROP TABLE IF EXISTS `email_notifications`;
CREATE TABLE `email_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `recipient_id` int(11) NOT NULL,
  `email_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_body` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `notification_type` enum('irs_approval','irs_decline','crypto_deposit_approval','crypto_deposit_decline','card_topup_approval','card_topup_decline','general') COLLATE utf8mb4_unicode_ci NOT NULL,
  `related_record_id` int(11) DEFAULT NULL,
  `related_record_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('pending','sent','failed','bounced') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `sent_at` timestamp NULL DEFAULT NULL,
  `error_message` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,

  PRIMARY KEY (`id`),
  KEY `idx_recipient_id` (`recipient_id`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_status` (`status`),
  KEY `idx_related_record` (`related_record_type`, `related_record_id`),
  CONSTRAINT `email_notifications_ibfk_1` FOREIGN KEY (`recipient_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 8. ADMIN CRYPTO WALLET ADDRESSES (for deposits)
-- =====================================================

DROP TABLE IF EXISTS `admin_crypto_addresses`;
CREATE TABLE `admin_crypto_addresses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cryptocurrency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `wallet_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `network` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `qr_code_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` boolean DEFAULT TRUE,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY (`id`),
  UNIQUE KEY `crypto_address_unique` (`cryptocurrency`, `wallet_address`),
  KEY `idx_cryptocurrency` (`cryptocurrency`),
  KEY `idx_is_active` (`is_active`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `admin_crypto_addresses_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- INSERT SAMPLE DATA
-- =====================================================

-- Sample payment methods
INSERT INTO `payment_methods` (`method_name`, `method_type`, `display_name`, `description`, `account_details`, `processing_fee`, `fee_type`, `minimum_amount`, `maximum_amount`, `is_active`, `sort_order`, `created_by`) VALUES
('bank_transfer', 'bank_account', 'Bank Transfer', 'Direct bank account transfer', '{"account_name": "Online Banking Corp", "account_number": "**********", "routing_number": "*********", "bank_name": "Chase Bank"}', 2.50, 'fixed', 10.00, 10000.00, 1, 1, 1),
('credit_card', 'payment_processor', 'Credit Card', 'Credit card payment processing', '{"processor": "Stripe", "merchant_id": "STRIPE123"}', 2.9, 'percentage', 5.00, 5000.00, 1, 2, 1),
('debit_card', 'payment_processor', 'Debit Card', 'Debit card payment processing', '{"processor": "Stripe", "merchant_id": "STRIPE123"}', 1.5, 'percentage', 5.00, 5000.00, 1, 3, 1),
('paypal', 'payment_processor', 'PayPal', 'PayPal payment processing', '{"paypal_email": "<EMAIL>", "merchant_id": "PAYPAL123"}', 3.5, 'percentage', 5.00, 5000.00, 1, 4, 1),
('crypto_btc', 'crypto_wallet', 'Bitcoin Payment', 'Bitcoin cryptocurrency payment', '{"wallet_address": "**********************************", "network": "mainnet"}', 0.00, 'fixed', 0.001, 100.00, 1, 5, 1);

-- Sample admin crypto addresses
INSERT INTO `admin_crypto_addresses` (`cryptocurrency`, `wallet_address`, `network`, `is_active`, `created_by`) VALUES
('BTC', '**********************************', 'mainnet', 1, 1),
('ETH', '******************************************', 'mainnet', 1, 1),
('LTC', 'LTC1qw508d6qejxtdg4y5r3zarvary0c5xw7kv8f3t4', 'mainnet', 1, 1),
('XRP', 'rN7n7otQDd6FczFgLdSqtcsAUxDkw6fzRH', 'mainnet', 1, 1),
('ADA', 'addr1qx2fxv2umyhttkxyxp8x0dlpdt3k6cwng5pxj3jhsydzer3jcu5d8ps7zex2k2xt3uqxgjqnnj83ws8lhrn648jjxtwq2ytjqp', 'mainnet', 1, 1),
('DOT', '***********************************************', 'mainnet', 1, 1);
