<?php
/**
 * Get Cheque Details API
 * Returns detailed information about a specific cheque deposit
 */

header('Content-Type: application/json');

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Include database connection
require_once '../../config/config.php';

try {
    // Get cheque ID from request
    $cheque_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if (!$cheque_id) {
        throw new Exception('Invalid cheque ID');
    }
    
    // Get database connection
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Fetch cheque details - ensure it belongs to the current user
    $query = "SELECT cd.*, a.account_number, a.first_name, a.last_name 
              FROM cheque_deposits cd
              JOIN accounts a ON cd.account_id = a.id
              WHERE cd.id = ? AND cd.account_id = ?";
    
    $result = $db->query($query, [$cheque_id, $user_id]);
    $cheque = $result->fetch_assoc();
    
    if (!$cheque) {
        throw new Exception('Cheque not found or access denied');
    }
    
    // Return success response with cheque data
    echo json_encode([
        'success' => true,
        'cheque' => [
            'id' => $cheque['id'],
            'cheque_number' => $cheque['cheque_number'],
            'amount' => $cheque['amount'],
            'currency' => $cheque['currency'],
            'sender_name' => $cheque['sender_name'],
            'bank_name' => $cheque['bank_name'],
            'deposit_date' => $cheque['deposit_date'],
            'clearance_status' => $cheque['clearance_status'],
            'account_type' => $cheque['account_type'],
            'branch_code' => $cheque['branch_code'],
            'id_passport_number' => $cheque['id_passport_number'],
            'cheque_image_path' => $cheque['cheque_image_path'],
            'created_at' => $cheque['created_at'],
            'account_holder' => $cheque['first_name'] . ' ' . $cheque['last_name'],
            'account_number' => $cheque['account_number']
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
