<?php
/**
 * Admin Payment Methods Management
 * Manage payment methods for crypto deposits and card top-ups
 */

// Set page variables
$page_title = 'Payment Methods';
$current_page = 'payment-methods';

// Start session and check authentication
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/dynamic-css.php';

// Get database connection
$db = getDB();
$admin_id = $_SESSION['admin_id'];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_method':
                    $method_name = trim($_POST['method_name']);
                    $method_type = trim($_POST['method_type']);
                    $display_name = trim($_POST['display_name']);
                    $description = trim($_POST['description']);
                    $processing_fee = floatval($_POST['processing_fee']);
                    $fee_type = trim($_POST['fee_type']);
                    $minimum_amount = floatval($_POST['minimum_amount']);
                    $maximum_amount = floatval($_POST['maximum_amount']);
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    // Build account details JSON
                    $account_details = [];
                    if ($method_type === 'bank_account') {
                        $account_details = [
                            'bank_name' => trim($_POST['bank_name']),
                            'account_name' => trim($_POST['account_name']),
                            'account_number' => trim($_POST['account_number']),
                            'routing_number' => trim($_POST['routing_number'])
                        ];
                    } elseif ($method_type === 'crypto_wallet') {
                        $account_details = [
                            'wallet_address' => trim($_POST['wallet_address']),
                            'network' => trim($_POST['network'])
                        ];
                    } else {
                        $account_details = [
                            'processor' => trim($_POST['processor']),
                            'merchant_id' => trim($_POST['merchant_id'])
                        ];
                    }
                    
                    $insert_query = "INSERT INTO payment_methods (
                        method_name, method_type, display_name, description, 
                        account_details, processing_fee, fee_type, minimum_amount, 
                        maximum_amount, is_active, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $result = $db->insert($insert_query, [
                        $method_name, $method_type, $display_name, $description,
                        json_encode($account_details), $processing_fee, $fee_type,
                        $minimum_amount, $maximum_amount, $is_active, $admin_id
                    ]);
                    
                    if ($result) {
                        $success_message = "Payment method added successfully!";
                    } else {
                        throw new Exception("Failed to add payment method.");
                    }
                    break;
                    
                case 'toggle_status':
                    $method_id = intval($_POST['method_id']);
                    $new_status = intval($_POST['new_status']);
                    
                    $update_query = "UPDATE payment_methods SET is_active = ? WHERE id = ?";
                    $result = $db->query($update_query, [$new_status, $method_id]);
                    
                    if ($result) {
                        $success_message = "Payment method status updated successfully!";
                    } else {
                        throw new Exception("Failed to update payment method status.");
                    }
                    break;
                    
                case 'delete_method':
                    $method_id = intval($_POST['method_id']);
                    
                    $delete_query = "DELETE FROM payment_methods WHERE id = ?";
                    $result = $db->query($delete_query, [$method_id]);
                    
                    if ($result) {
                        $success_message = "Payment method deleted successfully!";
                    } else {
                        throw new Exception("Failed to delete payment method.");
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get all payment methods
$methods_query = "SELECT pm.*, a.first_name, a.last_name 
                  FROM payment_methods pm 
                  LEFT JOIN accounts a ON pm.created_by = a.id 
                  ORDER BY pm.sort_order, pm.method_name";
$methods_result = $db->query($methods_query);
$payment_methods = [];
while ($row = $methods_result->fetch_assoc()) {
    $payment_methods[] = $row;
}

// Include admin header
require_once 'includes/admin-header.php';
?>

<!-- Include Admin CSS -->
<link rel="stylesheet" href="assets/css/admin.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Admin Sidebar -->
<?php require_once 'includes/admin-sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="admin-main-content">
    <!-- Admin Header Component -->
    <?php require_once 'includes/admin-header-component.php'; ?>

    <div class="admin-content">
        <!-- Page Header -->
        <div class="admin-page-header">
            <div class="page-header-content">
                <div class="page-header-main">
                    <h1 class="page-title">Payment Methods</h1>
                    <p class="page-subtitle">Manage payment methods for crypto deposits and card top-ups</p>
                </div>
                <div class="page-header-actions">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMethodModal">
                        <i class="fas fa-plus me-2"></i>Add Payment Method
                    </button>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Payment Methods Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Payment Methods (<?php echo count($payment_methods); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($payment_methods)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Payment Methods</h5>
                    <p class="text-muted">No payment methods have been configured yet.</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMethodModal">
                        <i class="fas fa-plus me-2"></i>Add First Payment Method
                    </button>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Method Name</th>
                                <th>Type</th>
                                <th>Display Name</th>
                                <th>Fee</th>
                                <th>Limits</th>
                                <th>Status</th>
                                <th>Created By</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payment_methods as $index => $method): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td>
                                    <strong><?php echo htmlspecialchars($method['method_name']); ?></strong>
                                    <?php if ($method['description']): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($method['description']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <?php echo ucfirst(str_replace('_', ' ', $method['method_type'])); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($method['display_name']); ?></td>
                                <td>
                                    <?php if ($method['processing_fee'] > 0): ?>
                                        <?php echo $method['fee_type'] === 'percentage' ? $method['processing_fee'] . '%' : '$' . number_format($method['processing_fee'], 2); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Free</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small>
                                        Min: $<?php echo number_format($method['minimum_amount'], 2); ?><br>
                                        Max: $<?php echo number_format($method['maximum_amount'], 2); ?>
                                    </small>
                                </td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="method_id" value="<?php echo $method['id']; ?>">
                                        <input type="hidden" name="new_status" value="<?php echo $method['is_active'] ? 0 : 1; ?>">
                                        <button type="submit" class="btn btn-sm btn-<?php echo $method['is_active'] ? 'success' : 'secondary'; ?>">
                                            <?php echo $method['is_active'] ? 'Active' : 'Inactive'; ?>
                                        </button>
                                    </form>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($method['first_name'] . ' ' . $method['last_name']); ?>
                                    <br><small class="text-muted"><?php echo date('M j, Y', strtotime($method['created_at'])); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="viewMethodDetails(<?php echo htmlspecialchars(json_encode($method)); ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <form method="POST" style="display: inline;" 
                                              onsubmit="return confirm('Are you sure you want to delete this payment method?')">
                                            <input type="hidden" name="action" value="delete_method">
                                            <input type="hidden" name="method_id" value="<?php echo $method['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Payment Method Modal -->
<div class="modal fade" id="addMethodModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Payment Method</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="addMethodForm">
                <input type="hidden" name="action" value="add_method">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="method_name" class="form-label">Method Name</label>
                                <input type="text" class="form-control" id="method_name" name="method_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="method_type" class="form-label">Method Type</label>
                                <select class="form-select" id="method_type" name="method_type" required>
                                    <option value="">Select Type</option>
                                    <option value="bank_account">Bank Account</option>
                                    <option value="crypto_wallet">Crypto Wallet</option>
                                    <option value="payment_processor">Payment Processor</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="display_name" class="form-label">Display Name</label>
                                <input type="text" class="form-control" id="display_name" name="display_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="processing_fee" class="form-label">Processing Fee</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="processing_fee" name="processing_fee"
                                           step="0.01" min="0" value="0">
                                    <select class="form-select" id="fee_type" name="fee_type" style="max-width: 120px;">
                                        <option value="fixed">Fixed ($)</option>
                                        <option value="percentage">Percentage (%)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="minimum_amount" class="form-label">Minimum Amount ($)</label>
                                <input type="number" class="form-control" id="minimum_amount" name="minimum_amount"
                                       step="0.01" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="maximum_amount" class="form-label">Maximum Amount ($)</label>
                                <input type="number" class="form-control" id="maximum_amount" name="maximum_amount"
                                       step="0.01" min="0" value="999999.99">
                            </div>
                        </div>
                    </div>

                    <!-- Bank Account Fields -->
                    <div id="bank_account_fields" style="display: none;">
                        <h6 class="mb-3">Bank Account Details</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bank_name" class="form-label">Bank Name</label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_name" class="form-label">Account Name</label>
                                    <input type="text" class="form-control" id="account_name" name="account_name">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_number" class="form-label">Account Number</label>
                                    <input type="text" class="form-control" id="account_number" name="account_number">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="routing_number" class="form-label">Routing Number</label>
                                    <input type="text" class="form-control" id="routing_number" name="routing_number">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Crypto Wallet Fields -->
                    <div id="crypto_wallet_fields" style="display: none;">
                        <h6 class="mb-3">Crypto Wallet Details</h6>
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="wallet_address" class="form-label">Wallet Address</label>
                                    <input type="text" class="form-control" id="wallet_address" name="wallet_address">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="network" class="form-label">Network</label>
                                    <input type="text" class="form-control" id="network" name="network" placeholder="mainnet">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Processor Fields -->
                    <div id="payment_processor_fields" style="display: none;">
                        <h6 class="mb-3">Payment Processor Details</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="processor" class="form-label">Processor Name</label>
                                    <input type="text" class="form-control" id="processor" name="processor" placeholder="Stripe, PayPal, etc.">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="merchant_id" class="form-label">Merchant ID</label>
                                    <input type="text" class="form-control" id="merchant_id" name="merchant_id">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            Active (available for use)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Payment Method</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Method Details Modal -->
<div class="modal fade" id="viewMethodModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Method Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="methodDetailsContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Show/hide fields based on method type
    document.getElementById('method_type').addEventListener('change', function() {
        const methodType = this.value;

        // Hide all field groups
        document.getElementById('bank_account_fields').style.display = 'none';
        document.getElementById('crypto_wallet_fields').style.display = 'none';
        document.getElementById('payment_processor_fields').style.display = 'none';

        // Show relevant field group
        if (methodType === 'bank_account') {
            document.getElementById('bank_account_fields').style.display = 'block';
        } else if (methodType === 'crypto_wallet') {
            document.getElementById('crypto_wallet_fields').style.display = 'block';
        } else if (methodType === 'payment_processor') {
            document.getElementById('payment_processor_fields').style.display = 'block';
        }
    });

    // View method details
    function viewMethodDetails(method) {
        const content = document.getElementById('methodDetailsContent');
        let accountDetails = '';

        try {
            const details = JSON.parse(method.account_details);
            if (method.method_type === 'bank_account') {
                accountDetails = `
                    <div class="row">
                        <div class="col-md-6"><strong>Bank Name:</strong> ${details.bank_name || 'N/A'}</div>
                        <div class="col-md-6"><strong>Account Name:</strong> ${details.account_name || 'N/A'}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Account Number:</strong> ${details.account_number || 'N/A'}</div>
                        <div class="col-md-6"><strong>Routing Number:</strong> ${details.routing_number || 'N/A'}</div>
                    </div>
                `;
            } else if (method.method_type === 'crypto_wallet') {
                accountDetails = `
                    <div class="row">
                        <div class="col-md-8"><strong>Wallet Address:</strong> <code>${details.wallet_address || 'N/A'}</code></div>
                        <div class="col-md-4"><strong>Network:</strong> ${details.network || 'N/A'}</div>
                    </div>
                `;
            } else {
                accountDetails = `
                    <div class="row">
                        <div class="col-md-6"><strong>Processor:</strong> ${details.processor || 'N/A'}</div>
                        <div class="col-md-6"><strong>Merchant ID:</strong> ${details.merchant_id || 'N/A'}</div>
                    </div>
                `;
            }
        } catch (e) {
            accountDetails = '<p class="text-muted">No account details available</p>';
        }

        content.innerHTML = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong>Method Name:</strong> ${method.method_name}
                </div>
                <div class="col-md-6">
                    <strong>Display Name:</strong> ${method.display_name}
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong>Type:</strong> <span class="badge bg-secondary">${method.method_type.replace('_', ' ')}</span>
                </div>
                <div class="col-md-6">
                    <strong>Status:</strong> <span class="badge bg-${method.is_active == 1 ? 'success' : 'secondary'}">${method.is_active == 1 ? 'Active' : 'Inactive'}</span>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong>Processing Fee:</strong> ${method.processing_fee > 0 ? (method.fee_type === 'percentage' ? method.processing_fee + '%' : '$' + parseFloat(method.processing_fee).toFixed(2)) : 'Free'}
                </div>
                <div class="col-md-6">
                    <strong>Limits:</strong> $${parseFloat(method.minimum_amount).toFixed(2)} - $${parseFloat(method.maximum_amount).toFixed(2)}
                </div>
            </div>
            ${method.description ? `<div class="mb-3"><strong>Description:</strong> ${method.description}</div>` : ''}
            <div class="mb-3">
                <strong>Account Details:</strong>
                <div class="mt-2 p-3 bg-light rounded">
                    ${accountDetails}
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <strong>Created:</strong> ${new Date(method.created_at).toLocaleDateString()}
                </div>
                <div class="col-md-6">
                    <strong>Last Updated:</strong> ${new Date(method.updated_at).toLocaleDateString()}
                </div>
            </div>
        `;

        new bootstrap.Modal(document.getElementById('viewMethodModal')).show();
    }

    // Auto-dismiss alerts
    document.addEventListener('DOMContentLoaded', function() {
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });
    });
</script>

<?php require_once 'includes/admin-footer.php'; ?>
