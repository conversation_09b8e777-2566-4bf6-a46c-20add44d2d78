<?php
require_once 'config/database.php';

echo "Adding virtual_card_balance column to accounts table...\n";

try {
    $db = getDB();
    
    // Check if column exists
    $result = $db->query("SHOW COLUMNS FROM accounts LIKE 'virtual_card_balance'");
    
    if ($result && $result->num_rows > 0) {
        echo "✅ virtual_card_balance column already exists\n";
    } else {
        // Add the column
        $db->query('ALTER TABLE accounts ADD COLUMN virtual_card_balance DECIMAL(15,2) DEFAULT 0.00 AFTER balance');
        echo "✅ virtual_card_balance column added successfully\n";
    }
    
    // Set some initial virtual card balances for testing
    $db->query("UPDATE accounts SET virtual_card_balance = 500.00 WHERE username = 'jamesbong101'");
    echo "✅ Test virtual card balance set for jamesbong101\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
