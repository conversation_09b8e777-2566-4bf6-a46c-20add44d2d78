<?php
// admin_sidebar.php

function isActivePage($page) {
    return basename($_SERVER['PHP_SELF']) == $page ? 'active' : '';
}
?>

<div class="admin-sidebar">
    <ul class="admin-nav">
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('dashboard.php'); ?>" href="dashboard.php">
                <i class="fas fa-tachometer-alt admin-nav-icon"></i>
                Dashboard
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('users.php'); ?>" href="users.php">
                <i class="fas fa-users admin-nav-icon"></i>
                Users
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('settings.php'); ?>" href="settings.php">
                <i class="fas fa-cog admin-nav-icon"></i>
                Settings
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('security-settings.php'); ?>" href="security-settings.php">
                <i class="fas fa-cog admin-nav-icon"></i>
                Global Security
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('configure-2fa.php'); ?>" href="configure-2fa.php">
                <i class="fas fa-shield-alt admin-nav-icon"></i>
                Configure 2FA
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('feature-settings.php'); ?>" href="feature-settings.php">
                <i class="fas fa-toggle-on admin-nav-icon"></i>
                Feature Settings
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('support-tickets.php'); ?>" href="support-tickets.php">
                <i class="fas fa-headset admin-nav-icon"></i>
                Help & Support
                <?php
                // Get count of open support tickets
                $db = getDB();
                $ticket_count_query = "SELECT COUNT(*) as count FROM support_tickets WHERE status IN ('open', 'in_progress')";
                $ticket_count_result = $db->query($ticket_count_query);
                $ticket_count = 0;
                if ($ticket_count_result && $row = $ticket_count_result->fetch_assoc()) {
                    $ticket_count = $row['count'];
                }
                if ($ticket_count > 0):
                ?>
                <span class="badge badge-danger ms-2"><?php echo $ticket_count; ?></span>
                <?php endif; ?>
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('irs-applications.php'); ?>" href="irs-applications.php">
                <i class="fas fa-file-invoice-dollar admin-nav-icon"></i>
                IRS Applications
                <?php
                // Get count of pending IRS applications
                try {
                    $irs_count_query = "SELECT COUNT(*) as count FROM irs_applications WHERE status = 'pending'";
                    $irs_count_result = $db->query($irs_count_query);
                    $irs_count = 0;
                    if ($irs_count_result && $row = $irs_count_result->fetch_assoc()) {
                        $irs_count = $row['count'];
                    }
                    if ($irs_count > 0):
                    ?>
                    <span class="badge badge-warning ms-2"><?php echo $irs_count; ?></span>
                    <?php endif;
                } catch (Exception $e) {
                    // Ignore errors if table doesn't exist yet
                }
                ?>
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('crypto-transfers.php'); ?>" href="crypto-transfers.php">
                <i class="fas fa-coins admin-nav-icon"></i>
                Crypto Transfers
                <?php
                // Get count of pending crypto transfers
                try {
                    $crypto_count_query = "SELECT COUNT(*) as count FROM crypto_transfers WHERE status = 'pending'";
                    $crypto_count_result = $db->query($crypto_count_query);
                    $crypto_count = 0;
                    if ($crypto_count_result && $row = $crypto_count_result->fetch_assoc()) {
                        $crypto_count = $row['count'];
                    }
                    if ($crypto_count > 0):
                    ?>
                    <span class="badge badge-warning ms-2"><?php echo $crypto_count; ?></span>
                    <?php endif;
                } catch (Exception $e) {
                    // Ignore errors if table doesn't exist yet
                }
                ?>
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('crypto-deposits.php'); ?>" href="crypto-deposits.php">
                <i class="fas fa-download admin-nav-icon"></i>
                Crypto Deposits
                <?php
                // Get count of pending crypto deposits
                try {
                    $crypto_deposits_count_query = "SELECT COUNT(*) as count FROM crypto_deposits WHERE status = 'pending'";
                    $crypto_deposits_count_result = $db->query($crypto_deposits_count_query);
                    $crypto_deposits_count = 0;
                    if ($crypto_deposits_count_result && $row = $crypto_deposits_count_result->fetch_assoc()) {
                        $crypto_deposits_count = $row['count'];
                    }
                    if ($crypto_deposits_count > 0):
                    ?>
                    <span class="badge badge-warning ms-2"><?php echo $crypto_deposits_count; ?></span>
                    <?php endif;
                } catch (Exception $e) {
                    // Ignore errors if table doesn't exist yet
                }
                ?>
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('card-topups.php'); ?>" href="card-topups.php">
                <i class="fas fa-credit-card admin-nav-icon"></i>
                Card Top-ups
                <?php
                // Get count of pending card top-ups
                try {
                    $topups_count_query = "SELECT COUNT(*) as count FROM card_topups WHERE status = 'pending'";
                    $topups_count_result = $db->query($topups_count_query);
                    $topups_count = 0;
                    if ($topups_count_result && $row = $topups_count_result->fetch_assoc()) {
                        $topups_count = $row['count'];
                    }
                    if ($topups_count > 0):
                    ?>
                    <span class="badge badge-warning ms-2"><?php echo $topups_count; ?></span>
                    <?php endif;
                } catch (Exception $e) {
                    // Ignore errors if table doesn't exist yet
                }
                ?>
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('payment-methods.php'); ?>" href="payment-methods.php">
                <i class="fas fa-money-bill-wave admin-nav-icon"></i>
                Payment Methods
            </a>
        </li>
    </ul>
</div>