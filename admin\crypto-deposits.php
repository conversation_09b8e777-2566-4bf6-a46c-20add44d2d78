<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Crypto Deposits Management';

// Handle deposit approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        $deposit_id = intval($_POST['deposit_id']);
        $action = $_POST['action'];
        $admin_notes = trim($_POST['admin_notes'] ?? '');
        
        if (!in_array($action, ['approve', 'decline'])) {
            throw new Exception("Invalid action.");
        }
        
        // Get deposit details
        $deposit_query = "SELECT cd.*, a.first_name, a.last_name, a.email 
                         FROM crypto_deposits cd 
                         LEFT JOIN accounts a ON cd.account_id = a.id 
                         WHERE cd.id = ? AND cd.status = 'pending'";
        $deposit_result = $db->query($deposit_query, [$deposit_id]);
        $deposit = $deposit_result->fetch_assoc();
        
        if (!$deposit) {
            throw new Exception("Deposit not found or already processed.");
        }
        
        $new_status = $action === 'approve' ? 'approved' : 'declined';
        
        // Update deposit status
        $update_query = "UPDATE crypto_deposits SET 
                        status = ?, admin_notes = ?, reviewed_by = ?, reviewed_at = NOW() 
                        WHERE id = ?";
        $db->query($update_query, [$new_status, $admin_notes, $_SESSION['user_id'], $deposit_id]);
        
        // Send email notification (placeholder for now)
        $email_subject = $action === 'approve' ? 
            'Crypto Deposit Approved' : 
            'Crypto Deposit Update';
            
        $email_body = $action === 'approve' ?
            "Dear {$deposit['first_name']},\n\nYour cryptocurrency deposit of {$deposit['amount']} {$deposit['crypto_type']} has been approved and credited to your account.\n\nReference: {$deposit['deposit_reference']}\n\nThank you for using our services." :
            "Dear {$deposit['first_name']},\n\nWe have reviewed your cryptocurrency deposit request. Unfortunately, we are unable to process your deposit at this time.\n\nReason: {$admin_notes}\n\nIf you have any questions, please contact our support team.";
        
        // Log email notification
        $email_insert = "INSERT INTO email_notifications (
            recipient_id, email_address, subject, message_body, notification_type, 
            related_record_id, related_record_type, status
        ) VALUES (?, ?, ?, ?, ?, ?, 'crypto_deposit', 'pending')";
        
        $db->query($email_insert, [
            $deposit['account_id'],
            $deposit['email'],
            $email_subject,
            $email_body,
            $action === 'approve' ? 'crypto_approval' : 'crypto_decline',
            $deposit_id
        ]);
        
        $success = "Deposit " . ($action === 'approve' ? 'approved' : 'declined') . " successfully!";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Pagination and filtering
$records_per_page = 20;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $records_per_page;

$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$crypto_filter = isset($_GET['crypto']) ? $_GET['crypto'] : '';
$method_filter = isset($_GET['method']) ? $_GET['method'] : '';

// Build WHERE clause
$where_conditions = [];
$params = [];

if (!empty($status_filter)) {
    $where_conditions[] = "cd.status = ?";
    $params[] = $status_filter;
}

if (!empty($crypto_filter)) {
    $where_conditions[] = "cd.crypto_type = ?";
    $params[] = $crypto_filter;
}

if (!empty($method_filter)) {
    $where_conditions[] = "cd.deposit_method = ?";
    $params[] = $method_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM crypto_deposits cd $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get deposits
    $deposits_query = "SELECT cd.*, 
                      a.first_name, a.last_name, a.username, a.account_number,
                      admin.first_name as admin_first_name, admin.last_name as admin_last_name
                      FROM crypto_deposits cd 
                      LEFT JOIN accounts a ON cd.account_id = a.id 
                      LEFT JOIN accounts admin ON cd.reviewed_by = admin.id 
                      $where_clause
                      ORDER BY cd.created_at DESC 
                      LIMIT $records_per_page OFFSET $offset";
    
    $deposits_result = $db->query($deposits_query, $params);
    $deposits = [];
    while ($row = $deposits_result->fetch_assoc()) {
        $deposits[] = $row;
    }
    
    // Get statistics
    $stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                    SUM(CASE WHEN status = 'declined' THEN 1 ELSE 0 END) as declined,
                    SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as total_approved_amount
                    FROM crypto_deposits";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    $error = "Failed to load deposits: " . $e->getMessage();
    $deposits = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'pending' => 0, 'approved' => 0, 'declined' => 0, 'total_approved_amount' => 0];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Crypto Deposits</li>
    </ol>
</nav>

<!-- Success/Error Messages -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-download"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total']); ?></div>
                        <div class="text-muted">Total Deposits</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending']); ?></div>
                        <div class="text-muted">Pending Review</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['approved']); ?></div>
                        <div class="text-muted">Approved</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                            <i class="fas fa-times"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['declined']); ?></div>
                        <div class="text-muted">Declined</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">Filter Deposits</h3>
    </div>
    <div class="card-body">
        <form method="GET" action="" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                    <option value="declined" <?php echo $status_filter === 'declined' ? 'selected' : ''; ?>>Declined</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="crypto" class="form-label">Cryptocurrency</label>
                <select class="form-select" id="crypto" name="crypto">
                    <option value="">All Cryptocurrencies</option>
                    <option value="BTC" <?php echo $crypto_filter === 'BTC' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                    <option value="ETH" <?php echo $crypto_filter === 'ETH' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                    <option value="LTC" <?php echo $crypto_filter === 'LTC' ? 'selected' : ''; ?>>Litecoin (LTC)</option>
                    <option value="XRP" <?php echo $crypto_filter === 'XRP' ? 'selected' : ''; ?>>Ripple (XRP)</option>
                    <option value="ADA" <?php echo $crypto_filter === 'ADA' ? 'selected' : ''; ?>>Cardano (ADA)</option>
                    <option value="DOT" <?php echo $crypto_filter === 'DOT' ? 'selected' : ''; ?>>Polkadot (DOT)</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="method" class="form-label">Deposit Method</label>
                <select class="form-select" id="method" name="method">
                    <option value="">All Methods</option>
                    <option value="bank_transfer" <?php echo $method_filter === 'bank_transfer' ? 'selected' : ''; ?>>Bank Transfer</option>
                    <option value="credit_card" <?php echo $method_filter === 'credit_card' ? 'selected' : ''; ?>>Credit Card</option>
                    <option value="debit_card" <?php echo $method_filter === 'debit_card' ? 'selected' : ''; ?>>Debit Card</option>
                    <option value="external_wallet" <?php echo $method_filter === 'external_wallet' ? 'selected' : ''; ?>>External Wallet</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-2"></i>Filter
                </button>
                <a href="crypto-deposits.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Deposits Table -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-download me-2"></i>
            Cryptocurrency Deposits
        </h3>
        <div class="card-subtitle">
            Manage and review cryptocurrency deposit requests
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($deposits)): ?>
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No deposits found</h5>
            <p class="text-muted">No cryptocurrency deposits match your current filters.</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-vcenter">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Cryptocurrency</th>
                        <th>Amount</th>
                        <th>Method</th>
                        <th>Reference</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($deposits as $deposit): ?>
                    <tr>
                        <td>
                            <div class="d-flex py-1 align-items-center">
                                <div class="flex-fill">
                                    <div class="font-weight-medium"><?php echo htmlspecialchars($deposit['first_name'] . ' ' . $deposit['last_name']); ?></div>
                                    <div class="text-muted"><?php echo htmlspecialchars($deposit['username']); ?></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary"><?php echo htmlspecialchars($deposit['crypto_type']); ?></span>
                        </td>
                        <td class="text-muted">
                            <div class="font-weight-medium"><?php echo number_format($deposit['amount'], 8); ?></div>
                            <div class="text-muted small"><?php echo htmlspecialchars($deposit['crypto_type']); ?></div>
                        </td>
                        <td>
                            <span class="badge bg-secondary"><?php echo ucfirst(str_replace('_', ' ', $deposit['deposit_method'])); ?></span>
                        </td>
                        <td>
                            <code class="small"><?php echo htmlspecialchars($deposit['deposit_reference']); ?></code>
                        </td>
                        <td class="text-muted">
                            <?php echo date('M j, Y', strtotime($deposit['created_at'])); ?>
                            <div class="small"><?php echo date('g:i A', strtotime($deposit['created_at'])); ?></div>
                        </td>
                        <td>
                            <span class="badge bg-<?php echo $deposit['status'] === 'approved' ? 'success' : ($deposit['status'] === 'declined' ? 'danger' : 'warning'); ?>">
                                <?php echo ucfirst($deposit['status']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-list flex-nowrap">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewDeposit(<?php echo $deposit['id']; ?>)">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if ($deposit['status'] === 'pending'): ?>
                                <button class="btn btn-sm btn-outline-success" onclick="reviewDeposit(<?php echo $deposit['id']; ?>, 'approve')">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="reviewDeposit(<?php echo $deposit['id']; ?>, 'decline')">
                                    <i class="fas fa-times"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Deposit Details Modal -->
<div class="modal fade" id="depositModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Deposit Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="depositModalBody">
                <!-- Deposit details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Review Deposit Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewModalTitle">Review Deposit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="deposit_id" id="reviewDepositId">
                    <input type="hidden" name="action" id="reviewAction">

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                  placeholder="Add notes about this decision (optional for approval, required for decline)"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="reviewActionText">This action will update the deposit status and notify the user via email.</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="reviewSubmitBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewDeposit(depositId) {
    // Load deposit details via AJAX (placeholder for now)
    document.getElementById('depositModalBody').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading deposit details...</p>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('depositModal'));
    modal.show();

    // Simulate loading delay
    setTimeout(() => {
        document.getElementById('depositModalBody').innerHTML = `
            <p>Deposit details for ID: ${depositId}</p>
            <p>This feature will be implemented in a future update.</p>
        `;
    }, 1000);
}

function reviewDeposit(depositId, action) {
    document.getElementById('reviewDepositId').value = depositId;
    document.getElementById('reviewAction').value = action;

    const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
    const title = document.getElementById('reviewModalTitle');
    const submitBtn = document.getElementById('reviewSubmitBtn');
    const actionText = document.getElementById('reviewActionText');
    const notesField = document.getElementById('admin_notes');

    if (action === 'approve') {
        title.textContent = 'Approve Deposit';
        submitBtn.textContent = 'Approve Deposit';
        submitBtn.className = 'btn btn-success';
        actionText.textContent = 'This will approve the deposit and credit the cryptocurrency to the user\'s account.';
        notesField.placeholder = 'Add approval notes (optional)';
        notesField.required = false;
    } else {
        title.textContent = 'Decline Deposit';
        submitBtn.textContent = 'Decline Deposit';
        submitBtn.className = 'btn btn-danger';
        actionText.textContent = 'This will decline the deposit and notify the user. Please provide a reason for the decline.';
        notesField.placeholder = 'Reason for decline (required)';
        notesField.required = true;
    }

    modal.show();
}

// Auto-dismiss alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
});
</script>

<?php include 'includes/admin-footer.php'; ?>
